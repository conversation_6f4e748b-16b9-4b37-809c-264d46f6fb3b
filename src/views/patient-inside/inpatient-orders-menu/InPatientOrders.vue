<template>
  <div class="inpatient-view">
    <in-patient-orders-tips />
    <el-tabs v-model="tabActive" type="border-card" @tab-click="handleChangeTab">
      <el-tab-pane name="cq" label="长期医嘱">
        <div class="filter-box">
          <el-button type="primary" size="mini" @click="handleActionClicked('refresh')">
            刷新页面
          </el-button>
          <el-checkbox v-model="allOrders" @change="handleAllOrdersChanged">所有医嘱</el-checkbox>
          <el-checkbox v-model="stoppedOrders" @change="handleStoppedOrdersChanged">
            显示停止医嘱
          </el-checkbox>
          <el-button type="primary" size="mini" @click="handleActionClicked('stop')">
            立即停(9)
          </el-button>
          <el-button type="primary" size="mini" @click="handleActionClicked('disSubmit')">
            撤回(0)
          </el-button>
          <el-button type="primary" size="mini" @click="handleActionClicked('execute')">
            执行后停(P)
          </el-button>
          <el-button type="primary" size="mini" @click="handleActionClicked('nextDay')">
            次日停(M)
          </el-button>
          <el-dropdown trigger="click" size="mini" @command="handleHistoryDrug">
            <el-button size="mini">
              历史用药
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">历史用药</el-dropdown-item>
                <el-dropdown-item command="2">在用口服药</el-dropdown-item>
                <el-dropdown-item command="3">在用针剂</el-dropdown-item>
                <el-dropdown-item command="4">历次住院用药</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" size="mini" @click="handleActionClicked('drugInfo')">
            药品说明书(Y)
          </el-button>
          <el-dropdown trigger="click" size="mini" @command="handleOtherFunc">
            <el-button size="mini">
              其他功能
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">患者医嘱360</el-dropdown-item>
                <el-dropdown-item command="2">闭环展示</el-dropdown-item>
                <el-dropdown-item command="3">不良事件上报</el-dropdown-item>
                <el-dropdown-item command="4">生成临时医嘱</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown trigger="click" size="mini" @command="handleVirtualDrugFunc">
            <el-button size="mini">
              虚拟药库
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">外配药品</el-dropdown-item>
                <el-dropdown-item command="2">赠送药品</el-dropdown-item>
                <!--<el-dropdown-item command="3">外购药品</el-dropdown-item>-->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" size="mini" @click="handleActionClicked('chinesePatentDrug')">
            中成药分餐
          </el-button>
          <el-button type="primary" size="mini" @click="handleActionClicked('changWaiDialog')">
            肠外营养套餐
          </el-button>
          <el-button
            type="primary"
            size="mini"
            class="purple-button"
            @click="handleActionClicked('tableConfig')"
          >
            表格配置
          </el-button>
        </div>
        <div class="inpatient-content">
          <!--<div class="title">长期医嘱</div>-->
          <div class="table-container">
            <el-table
              ref="standingTable"
              :data="yiZhuList"
              border
              size="mini"
              :height="yiZhuListHeight"
              :row-class-name="tableRowClassName"
              :span-method="handleSpanMethod"
              @row-click="handleRowClicked"
              @filter-change="handleTableFilterChange"
              @row-contextmenu="handleRowContextMenu"
            >
              <el-table-column
                :selectable="isRowSelectable"
                type="selection"
                width="40"
                align="center"
              ></el-table-column>
              <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.value"
                :label="column.label"
                align="center"
                v-bind="column.props"
              >
                <template #default="{ row, $index }">
                  <!-- 需要隐藏重复值的字段特殊处理 -->
                  <template v-if="hideRepeatedFields.includes(column.value)">
                    {{ getDisplayDate(row, $index, column.value) }}
                  </template>
                  <!-- 动态组件 -->
                  <template v-else-if="column.component">
                    <component :is="column.component" :row="row" :column="column" />
                  </template>
                  <!-- 默认显示 -->
                  <template v-else>
                    {{ row[column.value] }}
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <drawer-table
              ref="drawerTable"
              v-model="drawerYiZhuList"
              :visible.sync="orderDrawer"
              :inpatient-init="inpatientInit"
              :tab-active="tabActive"
              @row-click="drawerRowClicked"
              @selection-change="drawerSelectionChange"
              @requestCurrentTime="handleRequestCurrentTime"
              @requestAddItem="handleRequestAddItem"
            />
          </div>
          <div class="footer-action">
            <order-footer-action @actionClicked="handleActionClicked"></order-footer-action>
            <el-checkbox v-model="copyLastTime">复制上条时间</el-checkbox>
            <el-checkbox v-model="copyLastBingQu">复制上条病区</el-checkbox>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="ls" label="临时医嘱">
        <div class="filter-box">
          <el-button type="primary" size="mini" @click="handleActionClicked('refresh')">
            刷新页面
          </el-button>
          <el-checkbox v-model="allOrders" size="mini">所有医嘱</el-checkbox>
          <el-checkbox v-model="stoppedOrders" size="mini">显示停止医嘱</el-checkbox>
          <el-button type="primary" size="mini" @click="handleActionClicked('cancel')">
            撤销(9)
          </el-button>
          <el-button type="primary" size="mini" @click="handleActionClicked('disSubmit')">
            撤回(0)
          </el-button>
          <el-button type="primary" size="mini" @click="handleActionClicked('linShiExecute')">
            医嘱打√执行
          </el-button>
          <el-button type="primary" size="mini" @click="handleActionClicked('cancelExecute')">
            撤销执行
          </el-button>
          <el-dropdown trigger="click" size="mini" @command="handleHistoryDrug">
            <el-button size="mini">
              历史用药
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">历史用药</el-dropdown-item>
                <el-dropdown-item command="2">在用口服药</el-dropdown-item>
                <el-dropdown-item command="3">在用针剂</el-dropdown-item>
                <el-dropdown-item command="4">历次住院用药</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" size="mini" @click="handleActionClicked('drugInfo')">
            药品说明书(Y)
          </el-button>
          <el-dropdown trigger="click" size="mini" @command="handleOtherFunc">
            <el-button size="mini">
              其他功能
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">患者医嘱360</el-dropdown-item>
                <el-dropdown-item command="2">闭环展示</el-dropdown-item>
                <el-dropdown-item command="3">不良事件上报</el-dropdown-item>
                <el-dropdown-item v-if="tabActive === 'cq'" command="4">
                  生成临时医嘱
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown trigger="click" size="mini" @command="handleVirtualDrugFunc">
            <el-button size="mini">
              虚拟药库
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">外配药品</el-dropdown-item>
                <el-dropdown-item command="2">赠送药品</el-dropdown-item>
                <!-- <el-dropdown-item command="3">外购药品</el-dropdown-item> -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" size="mini" @click="handleActionClicked('tongYongTJ')">
            通用汤剂
          </el-button>
          <el-button
            type="primary"
            size="mini"
            class="purple-button"
            @click="handleActionClicked('tableConfig')"
          >
            表格配置
          </el-button>
        </div>
        <div class="inpatient-content">
          <!--<div class="title">临时医嘱</div>-->
          <div class="table-container">
            <el-table
              ref="tempTable"
              :data="yiZhuList"
              border
              size="mini"
              :height="yiZhuListHeight"
              :row-class-name="tableRowClassName"
              :span-method="handleSpanMethod"
              @row-click="handleRowClicked"
              @filter-change="handleTableFilterChange"
              @row-contextmenu="handleRowContextMenu"
            >
              <el-table-column
                :selectable="isRowSelectable"
                type="selection"
                width="40"
                align="center"
              ></el-table-column>
              <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.value"
                :label="column.label"
                align="center"
                v-bind="column.props"
              >
                <template #default="{ row, $index }">
                  <!-- 需要隐藏重复值的字段特殊处理 -->
                  <template v-if="hideRepeatedFields.includes(column.value)">
                    {{ getDisplayDate(row, $index, column.value) }}
                  </template>
                  <!-- 动态组件 -->
                  <template v-else-if="column.component">
                    <component :is="column.component" :row="row" :column="column" />
                  </template>
                  <!-- 默认显示 -->
                  <template v-else>
                    {{ row[column.value] }}
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <drawer-table
              ref="drawerTableTemp"
              v-model="drawerYiZhuList"
              :visible.sync="orderDrawer"
              :inpatient-init="inpatientInit"
              :tab-active="tabActive"
              @row-click="drawerRowClicked"
              @selection-change="drawerSelectionChange"
              @requestCurrentTime="handleRequestCurrentTime"
              @requestAddItem="handleRequestAddItem"
            />
          </div>
          <div class="footer-action">
            <order-footer-action @actionClicked="handleActionClicked"></order-footer-action>
            <el-checkbox v-model="copyLastTime">复制上条时间</el-checkbox>
            <el-checkbox v-model="copyLastBingQu">复制上条病区</el-checkbox>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="hy" label="化验医嘱">
        <laboratory-medical-advice ref="hyyz" :patient-init="initInfo" />
      </el-tab-pane>
      <el-tab-pane name="yj" label="医技检查">
        <medical-technology ref="yjjc" :patient-init="initInfo" />
      </el-tab-pane>
      <el-tab-pane name="yzd" label="医嘱单">
        <order-sheet />
      </el-tab-pane>
    </el-tabs>
    <!--临时医嘱执行弹窗-->
    <default-dialog
      title="医嘱执行"
      :visible.sync="executeDialogVisible"
      width="400px"
      @confirm="handleExecuteConfirm"
      @cancel="handleExecuteDialogClose"
    >
      <div class="execute-dialog-content">
        <span class="label">执行时间：</span>
        <el-date-picker
          v-model="executeTime"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择执行时间"
          style="width: 220px"
        />
      </div>
    </default-dialog>
    <!--历史用药-->
    <default-dialog
      class="history-drug-dialog"
      :visible.sync="historyDrugDialog"
      :title="historyDrugTitle"
      width="900px"
      pop-type="tip"
    >
      <div v-if="historyDrugTitle === '历史用药'" class="drug-filter">
        <el-radio-group v-model="historyDrugType" @change="handleHistoryDrugTypeChange">
          <el-radio label="cqzy">长期在用药品</el-radio>
          <el-radio label="lscq">历史用药(长期)</el-radio>
          <el-radio label="lsls">历史用药(临时)</el-radio>
        </el-radio-group>
        <div>
          <span>日期范围：</span>
          <el-date-picker
            v-model="historyDrugDateRange"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleHistoryDrugDateRangeChange"
          ></el-date-picker>
        </div>
      </div>
      <div v-if="historyDrugTitle === '历次住院用药'" class="drug-filter">
        <el-button type="primary" @click="handleBackToPreviousHospitalList">返回</el-button>
        <el-radio-group v-model="historyDrugType" @change="handleHistoryDrugTypeChange">
          <el-radio label="lscq">历史用药（长期）</el-radio>
          <el-radio label="lsls">历史用药（临时）</el-radio>
          <el-radio label="cydy">出院带药</el-radio>
        </el-radio-group>
        <div>
          <span>日期范围：</span>
          <el-date-picker
            v-model="historyDrugDateRange"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleHistoryDrugDateRangeChange"
          ></el-date-picker>
        </div>
      </div>
      <div>
        <el-table
          :data="historyDrugList"
          border
          stripe
          size="mini"
          max-height="600px"
          :span-method="historyDrugSpanMethod"
        >
          <el-table-column
            label="药品名称"
            prop="mingCheng"
            width="300"
            align="center"
          ></el-table-column>
          <el-table-column label="规格" width="100" prop="jiLiang" align="center"></el-table-column>
          <el-table-column label="单价" prop="shouJia" align="center"></el-table-column>
          <el-table-column label="数量" prop="yiCiYL" align="center"></el-table-column>
          <el-table-column label="用法用量" width="160" prop="yongFaYongLiang" align="center">
            <template #default="{ row }">
              <span>
                {{ row.yiCiYL && row.yiCiYL > 0 ? row.yiCiYL : '适量' }}{{ row.jiLiangDW }}
                {{ row.pinLv }} {{ row.fangFa }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="选择" prop="select" align="center">
            <template #default="{ row }">
              <el-button type="primary" plain @click="handleHistoryDrugSelect(row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!--历次住院记录选择弹窗-->
    <default-dialog
      class="previous-hospital-dialog"
      :visible.sync="previousHospitalDialog"
      title="历次住院记录"
      width="650px"
      pop-type="tip"
    >
      <div>
        <el-table :data="previousHospitalList" border stripe size="mini" max-height="400px">
          <el-table-column label="姓名" prop="xingMing" align="center"></el-table-column>
          <el-table-column label="科室名称" prop="keShiMC" align="center"></el-table-column>
          <el-table-column label="病区入院时间" prop="bingQuRYRQ" align="center"></el-table-column>
          <el-table-column label="选择" width="100" align="center">
            <template #default="{ row }">
              <el-button type="primary" plain @click="handleSelectHospitalRecord(row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!--虚拟药库-->
    <default-dialog
      class="virtual-drug-dialog"
      :visible.sync="virtualDrugDialog"
      :title="virtualDrugTitle"
      :width="virtualDrugTitle === '外配药品' ? '1000px' : '600px'"
      pop-type="tip"
    >
      <div v-if="virtualDrugTitle === '外配药品'" class="drug-filter">
        <el-input
          v-model="virtualDrugInput"
          placeholder="请输入药品拼音首字母或者中文"
          @keyup.enter.native="handleVirtualDrugSearch"
        ></el-input>
        <el-button type="primary" @click="handleVirtualDrugSearch">搜索</el-button>
      </div>
      <div>
        <el-table
          :data="virtualDrugList"
          border
          stripe
          size="mini"
          max-height="600px"
          :span-method="virtualDrugSpanMethod"
        >
          <el-table-column
            label="药品名称"
            prop="mingCheng"
            width="300"
            align="center"
          ></el-table-column>
          <el-table-column label="规格" prop="guiGe" align="center"></el-table-column>
          <el-table-column label="剂型" prop="jiXing" align="center"></el-table-column>
          <el-table-column
            v-if="virtualDrugTitle === '外配药品'"
            label="社保代码"
            prop="sheBaoDM"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="virtualDrugTitle === '外配药品'"
            label="厂家信息"
            prop="changJiaXX"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="virtualDrugTitle === '外配药品'"
            label="是否国谈药品"
            prop="guoTan"
            align="center"
          ></el-table-column>
          <el-table-column label="选择" prop="select" align="center">
            <template #default="{ row }">
              <el-button type="primary" plain @click="handleVirtualDrugSelect(row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!--中成药分餐-->
    <default-dialog
      :visible.sync="chinesePatentDrugDialog"
      title="中成药分餐"
      width="600px"
      pop-type="tip"
    >
      <div>
        <el-table
          :data="chinesePatentDrugList"
          max-height="600px"
          border
          stripe
          size="mini"
          :span-method="chinesePatentDrugSpanMethod"
        >
          <el-table-column
            label="药品名称"
            width="160"
            prop="mingCheng"
            align="center"
          ></el-table-column>
          <el-table-column label="规格" prop="guiGe" align="center"></el-table-column>
          <el-table-column label="单价" prop="guiGe" align="center"></el-table-column>
          <el-table-column label="选择" prop="select" align="center">
            <template #default="{ row }">
              <el-button type="primary" plain @click="handleChinesePatentDrugSelect(row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!--肠外营养套餐-->
    <chang-wai-dialog
      :visible.sync="changWaiDialog"
      @changWaiImportSuccess="handleChangWaiImportSuccess"
    ></chang-wai-dialog>
    <!--表格配置弹窗-->
    <default-dialog
      :visible.sync="tableConfigDialog"
      title="表格配置"
      width="500px"
      confirm-button-text="确认"
      cancel-button-text="关闭"
      @confirm="handleTableConfigSave"
      @cancel="handleTableConfigClose"
    >
      <div class="table-config-dialog">
        <el-table :data="tableConfigList" max-height="600px" border stripe size="mini">
          <el-table-column width="60" align="center">
            <template #default="{ row }">
              <el-checkbox v-model="row.show"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="表格列名称" prop="key" align="center"></el-table-column>
          <el-table-column label="移动" width="100" align="center">
            <template #default="{ $index }">
              <div class="move-buttons">
                <el-button :disabled="$index === 0" @click="handleMoveUp($index)">
                  <i class="el-icon-arrow-up"></i>
                </el-button>
                <el-button
                  :disabled="$index === tableConfigList.length - 1"
                  @click="handleMoveDown($index)"
                >
                  <i class="el-icon-arrow-down"></i>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!-- 药品个人模板 -->
    <default-dialog
      :visible.sync="personalDialog"
      title="药品个人模板"
      :width="isDetail ? '800px' : '500px'"
      pop-type="custom"
    >
      <template #footer>
        <div v-if="!isDetail">
          <el-button @click="personalDialog = false">关闭</el-button>
        </div>
        <div v-else>
          <el-button type="primary" @click="goBackPersonal">返回个人模板</el-button>
          <el-button @click="personalDialog = false">关闭</el-button>
        </div>
      </template>
      <div v-if="!isDetail">
        <div class="personal-muban">
          <span>模板查询：</span>
          <el-input
            v-model="muBanFilter"
            placeholder="请输入拼音码或者中文检索"
            @keyup.enter.native="handleMuBanFilter"
          ></el-input>
          <el-button type="primary" class="purple-button" @click="handleMuBanFilter">
            查询
          </el-button>
        </div>
        <div>
          <el-table :data="personalMuban" max-height="600px" border stripe size="mini">
            <el-table-column label="类别" prop="leiBie" align="center">
              <template #default="{ row }">
                {{
                  row.yiZhuLB === '1'
                    ? '西药'
                    : row.yiZhuLB === '2'
                    ? '中成药'
                    : row.yiZhuLB === '3'
                    ? '草药'
                    : ''
                }}
              </template>
            </el-table-column>
            <el-table-column label="模板名称" prop="muBanMC" align="center"></el-table-column>
            <el-table-column label="拼音码" prop="pinYin" align="center"></el-table-column>
            <el-table-column label="详细" prop="select" align="center">
              <template #default="{ row }">
                <el-button type="primary" plain @click="handlePersonalSelect(row)">详细</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-else>
        <div class="personal-muban-detail">
          <span>模板名称：{{ selectedMuBanMC }}</span>
          <el-table
            :data="personalMuBanMingXi"
            max-height="600px"
            border
            stripe
            size="mini"
            :span-method="personalMuBanMingXiSpanMethod"
          >
            <el-table-column
              label="药品名称"
              prop="mingCheng"
              width="300"
              align="center"
            ></el-table-column>
            <el-table-column label="规格" prop="jiLiang" align="center"></el-table-column>
            <el-table-column label="单价" prop="shouJia" align="center"></el-table-column>
            <el-table-column label="数量" prop="yiCiYL" align="center"></el-table-column>
            <el-table-column label="用法用量" prop="yongFaYongLiang" align="center">
              <template #default="{ row }">
                <span>
                  {{ row.yiCiYL && row.yiCiYL > 0 ? row.yiCiYL : '适量' }}{{ row.jiLiangDW }}
                  {{ row.pinLv }} {{ row.fangFa }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="选择" prop="select" align="center">
              <template #default="{ row }">
                <el-button type="primary" plain @click="handlePersonalMXSelect(row)">
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </default-dialog>
    <!-- 常用药模板 -->
    <default-dialog
      :visible.sync="changYongYaoDialog"
      title="常用药模板"
      width="800px"
      pop-type="tip"
    >
      <div>
        <el-table :data="changYongYaoList" max-height="600px" border stripe size="mini">
          <el-table-column
            label="药品名称"
            prop="mingCheng"
            width="300"
            align="center"
          ></el-table-column>
          <el-table-column label="规格" prop="jiLiang" align="center"></el-table-column>
          <el-table-column label="单价" prop="shouJia" align="center"></el-table-column>
          <el-table-column label="数量" prop="yiCiYL" align="center"></el-table-column>
          <el-table-column label="用法用量" prop="yongFaYongLiang" align="center">
            <template #default="{ row }">
              <span>
                {{ row.yiCiYL && row.yiCiYL > 0 ? row.yiCiYL : '适量' }}{{ row.jiLiangDW }}
                {{ row.pinLv }} {{ row.fangFa }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="选择" prop="select" align="center">
            <template #default="{ row }">
              <el-button type="primary" plain @click="handleChangYongYaoSelect(row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!--通用汤剂模板-->
    <default-dialog
      :visible.sync="tongYongTJDialog"
      title="通用汤剂模板"
      width="800px"
      pop-type="custom"
    >
      <template #footer>
        <div v-if="!isDetail">
          <el-button @click="tongYongTJDialog = false">关闭</el-button>
        </div>
        <div v-else>
          <el-button type="primary" @click="goBackTangJi">返回通用汤剂模板</el-button>
          <el-button @click="tongYongTJDialog = false">关闭</el-button>
        </div>
      </template>
      <div v-if="!isDetail">
        <div class="personal-muban">
          <span>模板查询：</span>
          <el-input v-model="muBanFilter" @keyup.enter.native="handleTangJiMuBanFilter"></el-input>
          <el-button type="primary" class="purple-button" @click="handleTangJiMuBanFilter">
            查询
          </el-button>
        </div>
        <div>
          <el-table :data="tongYongTJMuBan" max-height="600px" border stripe size="mini">
            <el-table-column label="类别" prop="leiBie" align="center">
              <template #default="{ row }">
                {{
                  row.yiZhuLB === '1'
                    ? '西药'
                    : row.yiZhuLB === '2'
                    ? '中成药'
                    : row.yiZhuLB === '3'
                    ? '草药'
                    : ''
                }}
              </template>
            </el-table-column>
            <el-table-column label="模板名称" prop="muBanMC" align="center"></el-table-column>
            <el-table-column label="拼音码" prop="pinYin" align="center"></el-table-column>
            <el-table-column label="详细" prop="select" align="center">
              <template #default="{ row }">
                <el-button type="primary" plain @click="handleTangJiSelect(row)">详细</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-else>
        <div class="personal-muban-detail">
          <span>模板名称：{{ selectedMuBanMC }}</span>
          <el-table :data="tongYongTJMuBanMingXi" max-height="600px" border stripe size="mini">
            <el-table-column
              label="名称"
              prop="mingCheng"
              width="200"
              align="center"
            ></el-table-column>
            <el-table-column label="规格" prop="jiLiang" align="center"></el-table-column>
            <el-table-column label="包装量" prop="baoZhuangLiang" align="center">
              <template #default="{ row }">{{ row.jiLiang }} * {{ row.baoZhuangLiang }}</template>
            </el-table-column>
            <el-table-column label="数量" prop="yiCiYL" align="center"></el-table-column>
            <el-table-column label="用法用量" prop="yongFaYongLiang" align="center">
              <template #default="{ row }">
                <span>
                  {{ row.yiCiYL && row.yiCiYL > 0 ? `${row.yiCiYL}${row.jiLiangDW}` : '适量' }}
                  {{ row.fangFa }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="选择" prop="select" align="center">
              <template #default="{ row, $index }">
                <el-button
                  v-if="$index === 0"
                  type="primary"
                  size="mini"
                  plain
                  @click="handleTangJiMXSelect(row)"
                >
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </default-dialog>
    <!-- 治疗医嘱模板 -->
    <heal-order-template-dialog
      :visible.sync="zhiLiaoMubanDialog"
      :inpatient-init="inpatientInit"
      :tab-active="tabActive"
      @confirm="handleZhiLiaoMuBanSelect"
    ></heal-order-template-dialog>
    <!-- 综合医嘱模板 -->
    <comprehensive-order-template-dialog
      :visible.sync="zongHeMubanDialog"
      :inpatient-init="inpatientInit"
      :tab-active="tabActive"
      @confirm="handleZongHeMuBanSelect"
    ></comprehensive-order-template-dialog>
    <!--保存模板-->
    <default-dialog
      :visible.sync="saveMBDialog"
      title="保存模板"
      width="600px"
      @confirm="handleSaveMBConfirm"
      @cancel="handleSaveMBCancel"
    >
      <div class="save-mb-header">
        <div class="save-title">请选择保存模式</div>
        <el-radio-group v-model="saveType">
          <el-radio label="1">常用药</el-radio>
          <el-radio label="2">个人模板</el-radio>
        </el-radio-group>
        <!-- 如果选择个人模板，显示输入框 -->
        <el-input
          v-if="saveType === '2'"
          v-model="templateName"
          placeholder="请输入模板名称"
          style="margin-top: 10px"
        ></el-input>
      </div>
      <div class="save-mb-content">
        <el-table
          :data="saveMBList"
          max-height="400px"
          border
          stripe
          size="mini"
          :span-method="saveMBSpanMethod"
          @selection-change="handleSaveMBSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <!-- <el-table-column label="组号" width="100" prop="zuHao"></el-table-column> -->
          <el-table-column label="名称" prop="mingCheng"></el-table-column>
          <el-table-column label="规格" prop="guiGe"></el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!-- 中选药品列表 -->
    <default-dialog
      :visible.sync="zhongXuanDialog"
      title="中选药品列表"
      width="600px"
      pop-type="tip"
      @closeTip="handleZhongXuanDialogClosed"
    >
      <div>
        <el-table :data="zhongXuanYaoPinList" max-height="600px" border stripe size="mini">
          <el-table-column
            label="药品名称"
            width="160"
            prop="mingCheng"
            align="center"
          ></el-table-column>
          <el-table-column label="是否集采" prop="jiCai" align="center"></el-table-column>
          <el-table-column label="规格" prop="guiGe" align="center"></el-table-column>
          <el-table-column label="单价" prop="guiGe" align="center"></el-table-column>
          <el-table-column label="用法用量" prop="yongFaYongLiang" align="center">
            <template #default="{ row }">
              <span>
                {{ row.yiCiYL && row.yiCiYL > 0 ? row.yiCiYL : '适量' }}{{ row.jiLiangDW }}
                {{ row.pinLv }} {{ row.fangFa }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="选择" prop="select" align="center">
            <template #default="{ row }">
              <el-button type="primary" plain @click="handleZhongXuanYaoPinSelect(row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!-- 中医辩证信息 -->
    <default-dialog
      :visible.sync="zhongYiBZDialog"
      title="中医辩证信息"
      width="600px"
      @confirm="handleZhongYiBZDialogConfirm"
      @cancel="handleZhongYiBZDialogCancel"
    >
      <div class="zhong-yi-dialog">
        <div class="zhong-yi-dialog-header">
          <div class="header-left">药品名称：</div>
          <div class="header-right">{{ zhongYiBZData.yaoPinMC }}</div>
        </div>
        <div class="zhong-yi-dialog-content">
          <el-table :data="zhongYiBZTableData" border style="width: 100%" max-height="400px">
            <el-table-column prop="zuHao" label="组" width="60" align="center"></el-table-column>
            <el-table-column label="诊断" width="240">
              <template #default="{ row }">
                <el-checkbox-group v-model="row.selectedZhenDuanIDs">
                  <div v-for="(item, index) in row.zhenDuanList" :key="'zd_' + index">
                    <el-checkbox :label="item.zhenDuanID">
                      {{ item.zhenDuanMC }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </template>
            </el-table-column>
            <el-table-column label="症状">
              <template #default="{ row }">
                <el-checkbox-group v-model="row.selectedZhengZhuangIDs">
                  <div v-for="(item, index) in row.zhengZhuangList" :key="'zz_' + index">
                    <el-checkbox :label="item.zhengZhuangID">
                      {{ item.zhengZhuangMC }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </default-dialog>
    <!-- 麻醉药品用途 -->
    <default-dialog
      :visible.sync="mzytDialog"
      title="麻醉药品用途"
      width="350px"
      confirm-button-text="保存"
      cancel-button-text="关闭"
      @confirm="handleMzytSave"
      @cancel="handleMzytClose"
    >
      <div class="mzyt-dialog">
        <el-radio-group v-model="mzytValue">
          <el-radio v-for="item in mzytList" :key="item.daiMa" :label="item.daiMa">
            {{ item.mingCheng }}
          </el-radio>
        </el-radio-group>
      </div>
    </default-dialog>
    <!--社保审批弹框-->
    <she-bao-dialog
      :visible.sync="sheBaoDialog"
      :display-list="displayList"
      :dialog-type="dialogType"
      :she-bao-yao-pin-data="sheBaoYaoPinData"
      :inpatient-init="inpatientInit"
      :shang-ci-xdfw="shangCiXdfw"
      @confirm="handleSheBaoConfirm"
      @close="handleSheBaoClose"
    ></she-bao-dialog>
    <!--体表面积计算弹窗-->
    <default-dialog
      :visible.sync="tbmjDialog"
      title="体表面积"
      width="700px"
      @confirm="handleTBMJDialogConfirm"
      @cancel="handleTBMJDialogCancel"
    >
      <el-descriptions class="tbmj-descriptions" :column="2" border>
        <el-descriptions-item label="患者姓名：">
          {{ inpatientInit?.inPatientVo?.bingRenXM }}
        </el-descriptions-item>
        <el-descriptions-item label="年龄：">
          {{ inpatientInit.age }}
        </el-descriptions-item>
        <el-descriptions-item label="身高(cm)：">
          <el-input v-model="lastSGTZ.shenGao" @input="handleTBMJInput"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="体重(kg)：">
          <el-input v-model="lastSGTZ.tiZhong" @input="handleTBMJInput"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="体表面积(m²)：" :span="2">
          {{ TBMJ }}
        </el-descriptions-item>
        <el-descriptions-item label="体表面积计算公式：" :span="2">
          {{ TBMJText }}
        </el-descriptions-item>
        <el-descriptions-item label="上次记录时间：">
          {{ lastSGTZ.ceLiangSJ }}
        </el-descriptions-item>
        <el-descriptions-item label="记录人：">
          {{ lastSGTZ.caoZuoZhe }}
        </el-descriptions-item>
      </el-descriptions>
    </default-dialog>

    <!--处方药诊断弹窗-->
    <default-dialog
      :visible.sync="chuFangZDDialog"
      title="处方诊断"
      width="500px"
      @confirm="handleChuFangZDDialogConfirm"
      @cancel="handleChuFangZDDialogCancel"
    >
      <div class="chu-fang-zd-dialog">
        <div class="chu-fang-zd-header">
          <div class="header-title">处方诊断</div>
          <el-button type="primary" size="mini" @click="handleAddChuFangZD">新增</el-button>
        </div>
        <div class="chu-fang-zd-content">
          <el-table :data="chuFangZDList" border size="mini" style="width: 100%">
            <el-table-column prop="mingChen" label="名称" min-width="200">
              <template #default="{ row, $index }">
                <span class="zd-name-link" @click="handleSelectChuFangZD($index)">
                  {{ row.mingChen }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="{ $index }">
                <el-button type="text" @click="handleDeleteChuFangZD($index)">删除</el-button>
              </template>
            </el-table-column>
            <el-table-column label="排序" align="center">
              <template #default="{ $index }">
                <div class="sort-buttons">
                  <el-button :disabled="$index === 0" @click="handleSortUp($index)">
                    <i class="el-icon-arrow-up"></i>
                  </el-button>
                  <el-button
                    :disabled="$index === chuFangZDList.length - 1"
                    @click="handleSortDown($index)"
                  >
                    <i class="el-icon-arrow-down"></i>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </default-dialog>
    <!--诊断列表弹窗-->
    <default-dialog
      :visible.sync="zhenDuanListDialog"
      title="选择处方诊断"
      width="700px"
      :close-on-click-modal="false"
      :append-to-body="true"
      pop-type="tip"
    >
      <div class="zhen-duan-list-dialog">
        <div class="search-bar">
          <el-input
            v-model="zhenDuanFilter"
            placeholder="请输入诊断名称"
            style="width: 400px; margin: 0 10px"
            @keyup.enter.native="searchZhenDuan"
          ></el-input>
          <el-button
            type="primary"
            :disabled="!isChuFangSelected"
            @click="handleSelectZhenDuanConfirm"
          >
            确定
          </el-button>
        </div>
        <div class="zhen-duan-table">
          <el-table
            :data="zhenDuanListData"
            border
            style="width: 100%"
            height="600px"
            @row-click="handleSelectZhenDuan"
          >
            <el-table-column prop="zhenDuanMC" label="诊断名称" min-width="400">
              <template #default="{ row }">
                <span class="zd-name-link">{{ row.zhenDuanMC }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="icd" label="诊断ICD" width="200"></el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            :current-page="pageIndex"
            :page-sizes="[20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </default-dialog>

    <!--草药快递弹窗-->
    <default-dialog
      :visible.sync="zykdDialog"
      title="草药快递"
      width="600px"
      :close-on-press-escape="false"
      :show-close="false"
      :show-cancel-button="false"
      @confirm="saveZykdAddress"
    >
      <el-descriptions class="zykd-descriptions" :column="2" border>
        <el-descriptions-item label="收货人：">
          <el-input v-model="zykdAddress.shouHuoRenXM" placeholder="请输入收货人"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="手机号码：">
          <el-input v-model="zykdAddress.lianXiDH" placeholder="请输入手机号码"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="所在地区：" :span="2">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-select
                v-model="zykdAddress.shengFen"
                placeholder="选择省"
                @change="handleProvinceChange"
              >
                <el-option
                  v-for="item in provinceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="zykdAddress.chengShi"
                placeholder="选择市"
                @change="handleCityChange"
              >
                <el-option
                  v-for="item in cityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="zykdAddress.qu" placeholder="选择区">
                <el-option
                  v-for="item in countyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-descriptions-item>
        <el-descriptions-item label="详细地址：" :span="2">
          <el-input v-model="zykdAddress.lianXiDZ" placeholder="请输入详细地址"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="备注：" :span="2">
          <el-input
            v-model="zykdAddress.beiZhu"
            type="textarea"
            rows="2"
            clearable
            maxlength="1000"
            show-word-limit
            placeholder="备注"
          ></el-input>
        </el-descriptions-item>
      </el-descriptions>
    </default-dialog>
    <!-- 治疗医嘱社保限制范围弹窗 -->
    <multiple-she-bao-dialog
      :visible.sync="zhiLiaoSheBaoDialog"
      :display-list="zhiLiaoYZMXWithSheBao"
      @confirm="handleZhiLiaoSheBaoConfirm"
      @close="handleZhiLiaoSheBaoClose"
    />
    <!-- 抗菌药物使用方法选择弹窗 -->
    <default-dialog
      :visible.sync="kangJunYpDialog"
      title="抗菌药物使用方法"
      width="800px"
      @confirm="handleKangJunYpConfirm"
      @cancel="handleKangJunYpCancel"
    >
      <div class="kang-jun-yp-dialog">
        <!-- 使用方法选择 -->
        <div class="usage-method-section">
          <div class="usage-method-options">
            <div
              v-for="item in kangJunYpData.shiYongFF"
              :key="item.daiMa"
              class="usage-method-item"
            >
              <el-radio
                v-model="kangJunYpForm.shiYongFFValue"
                :label="item.daiMa"
                class="usage-method-radio"
                @change="handleShiYongFFChange"
              >
                {{ item.mingCheng }}
              </el-radio>

              <!-- 预防选项的子选项 -->
              <div
                v-if="item.daiMa === '1'"
                class="prevention-sub-options"
                :class="{ disabled: kangJunYpForm.shiYongFFValue !== '1' }"
              >
                <el-row>
                  <!-- 第一列：切口类别 -->
                  <el-col :span="8">
                    <div class="option-column option-column-first">
                      <el-radio-group
                        v-model="kangJunYpForm.qieKouLBValue"
                        :disabled="kangJunYpForm.shiYongFFValue !== '1'"
                        @change="handleQieKouLBChange"
                      >
                        <div
                          v-for="subItem in kangJunYpData.qieKouLB"
                          :key="subItem.daiMa"
                          class="radio-item"
                        >
                          <el-radio :label="subItem.daiMa">
                            {{ subItem.mingCheng }}
                          </el-radio>
                        </div>
                      </el-radio-group>
                    </div>
                  </el-col>

                  <!-- 第二列：用法分类 -->
                  <el-col :span="8">
                    <div class="option-column option-column-middle">
                      <el-radio-group
                        v-model="kangJunYpForm.yongFaFLValue"
                        :disabled="kangJunYpForm.shiYongFFValue !== '1'"
                        @change="handleYongFaFLChange"
                      >
                        <div
                          v-for="subItem in kangJunYpData.yongFaFL"
                          :key="subItem.daiMa"
                          class="radio-item"
                        >
                          <el-radio
                            :label="subItem.daiMa"
                            :disabled="
                              subItem.daiMa === '4' ||
                              subItem.daiMa === '6' ||
                              subItem.daiMa === '8'
                            "
                          >
                            {{ subItem.mingCheng }}
                          </el-radio>
                        </div>
                      </el-radio-group>
                    </div>
                  </el-col>

                  <!-- 第三列：术前使用 -->
                  <el-col :span="8">
                    <div class="option-column option-column-last">
                      <el-radio-group
                        v-model="kangJunYpForm.shuQianSYValue"
                        :disabled="
                          kangJunYpForm.shiYongFFValue !== '1' ||
                          kangJunYpForm.yongFaFLValue !== '1'
                        "
                      >
                        <div
                          v-for="subItem in kangJunYpData.shuQianSY"
                          :key="subItem.daiMa"
                          class="radio-item"
                        >
                          <el-radio :label="subItem.daiMa">
                            {{ subItem.mingCheng }}
                          </el-radio>
                        </div>
                      </el-radio-group>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
            <!-- 手术列表 -->
            <div v-if="kangJunYpForm.yongFaFLValue === '1'" class="usage-method-item">
              <el-radio-group v-model="kangJunYpForm.shouShuMC" style="margin-left: 12px">
                <div v-for="(item, index) in yiLeiSSList" :key="index" class="radio-item">
                  <el-radio :label="item">
                    {{ item }}
                  </el-radio>
                </div>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>
    </default-dialog>
    <!-- 高危因素选择弹窗 -->
    <default-dialog
      :visible.sync="gaoWeiYSDialog"
      title="高危因素"
      width="740px"
      confirm-button-text="同意"
      @confirm="handleGaoWeiYSConfirm"
      @cancel="handleGaoWeiYSCancel"
    >
      <div class="gao-wei-ys-container">
        <!-- 高危因素提示信息 -->
        <el-alert type="primary" show-icon plain size="small" :closable="false">
          清洁手术（Ⅰ类切口）通常不需预防用抗菌药物。除非具备以下高危因素，经由科室正主任审批后方可使用。
        </el-alert>

        <!-- 高危因素选择 -->
        <div class="checkbox-container">
          <div class="title table-color-dark">
            <span style="color: red; margin-right: 6px">*</span>
            请选择高危因素（可多选）:
          </div>
          <el-checkbox-group v-model="selectedGaoWeiYS" class="checkbox-setting">
            <el-checkbox
              v-for="(item, index) in gaoWeiYSList"
              :key="index"
              :class="index % 2 ? 'table-color-light' : 'table-color-dark'"
              :label="item.daiMa"
            >
              {{ item.mingCheng }}
            </el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 科室主任审批 -->
        <div class="approval-section">
          <span style="color: red; margin-right: 6px">*</span>
          <span>科室正主任签名：</span>
          <el-input
            :value="keShiZRMC"
            style="width: 200px"
            readonly
            placeholder="请点击进行二次签名"
            @click.native="handleApprovalClick"
          ></el-input>
        </div>
      </div>
    </default-dialog>
    <!-- 二次签名弹窗 -->
    <secondary-password
      :visible.sync="secondaryPasswordVisible"
      @confirm="handleSecondaryPasswordConfirm"
      @cancel="handleSecondaryPasswordCancel"
    />
    <!-- 新生儿抗生素预防使用问卷弹窗 -->
    <default-dialog
      :visible.sync="newbornQuestionnaireDialog"
      title="新生儿抗生素预防使用问卷"
      width="700px"
      @confirm="handleNewbornQuestionnaireConfirm"
      @cancel="handleNewbornQuestionnaireCancel"
    >
      <div class="newborn-questionnaire-container">
        <!-- 第一个问题 -->
        <div class="question-section">
          <div class="question-title">
            <span>一、具有以下围产高危因素：</span>
            <el-radio-group
              v-model="newbornQuestionnaireForm.shiFouYWCG"
              class="question-radio"
              @change="handleQuestion1Change"
            >
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </div>
          <div class="question-content">
            <el-checkbox-group
              v-model="newbornQuestionnaireForm.weiChanGWYSS"
              :disabled="newbornQuestionnaireForm.shiFouYWCG !== '1'"
            >
              <div class="content-item">
                <el-checkbox label="1">
                  1.母亲有绒毛膜羊膜炎或发热伴感染症状（母亲体温≥38℃为异常诊疗标准，且同时具有下述中的2项即可诊断：每分钟白细胞计数≥15×10⁹/L；母亲心率≥100次/min；胎儿心动过速（≥160次/min）；母亲子宫触痛，羊水浑浊或恶臭）
                </el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="2">2.单纯产时发热（体温38.0-38.9℃）</el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="3">3.GBS定值或母尿中或任何部位LGBS感染</el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="4">4.胎膜早破≥18h</el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="5">
                  5.自发性早产（多胎、羊水过多、母过敏等非感染因素所致除外）
                </el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="6">6.双胎之一有感染</el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="7">7.有侵入性操作，如气管插管、脐静脉置管</el-checkbox>
              </div>
            </el-checkbox-group>
            <div class="content-item other" style="border-top: 1px solid #dcdfe6">
              <span>8.其他，具体诊断：</span>
              <el-input
                v-model="newbornQuestionnaireForm.qiTaWCGW"
                type="textarea"
                placeholder="请输入"
                maxlength="500"
                show-word-limit
                clearable
                :disabled="newbornQuestionnaireForm.shiFouYWCG !== '1'"
              />
            </div>
          </div>
        </div>

        <!-- 第二个问题 -->
        <div class="question-section">
          <div class="question-title">
            <span>二、患儿具有以下临床表现：</span>
            <el-radio-group
              v-model="newbornQuestionnaireForm.shiFouYLCBX"
              class="question-radio"
              @change="handleQuestion2Change"
            >
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </div>
          <div class="question-content">
            <el-checkbox-group
              v-model="newbornQuestionnaireForm.linChuangBX"
              :disabled="newbornQuestionnaireForm.shiFouYLCBX !== '1'"
            >
              <div class="content-item">
                <el-checkbox label="1">1.气促、呼吸三凹征、喘鸣、呼吸暂停、青紫</el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="2">2.无法解释的心动过速或心动过缓</el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="3">3.无法用环境因素解释的体温异常，来源体温杯不良</el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="4">
                  4.精神萎靡、哭声弱或易激惹的喂养、反应差或激惹、肌张力改变
                </el-checkbox>
              </div>
              <div class="content-item">
                <el-checkbox label="5">5.喂乳差、明显呕吐</el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </default-dialog>
    <!-- 感染诊断选择弹窗 -->
    <default-dialog
      :visible.sync="ganRanZDSelectDialog"
      title="感染诊断选择"
      width="600px"
      pop-type="tip"
    >
      <div class="gan-ran-zd-select-dialog">
        <div class="table-container">
          <el-table
            :data="ganRanZDList"
            highlight-current-row
            max-height="500"
            @row-click="handleGanRanZDSelected"
          >
            <el-table-column prop="mingChen" label="诊断名称" />
          </el-table>
        </div>
      </div>
    </default-dialog>
    <!-- 感染诊断库 -->
    <diagnostics-selector :visible.sync="ganRanZDLibraryDialog" size="mini" />
    <!--微生物送检情况弹窗-->
    <default-dialog
      :visible.sync="weiShengWuDialog"
      title="微生物送检情况"
      width="700px"
      confirm-button-text="确认"
      cancel-button-text="取消"
      @confirm="handleWeiShengWuConfirm"
      @cancel="handleWeiShengWuCancel"
    >
      <div class="wei-sheng-wu-container">
        <!-- 微生物送检情况选择 -->
        <div class="question-section">
          <div class="question-title">
            <span>出现送检情况：</span>
          </div>
          <div class="question-content">
            <div
              v-for="option in weiShengWuOptions"
              :key="option.daiMa"
              class="content-item"
              :class="{ selected: weiShengWuForm.selectedOption === option.daiMa }"
            >
              <el-radio
                v-model="weiShengWuForm.selectedOption"
                :label="option.daiMa"
                class="option-radio"
              >
                {{ option.mingCheng }}
              </el-radio>
            </div>
          </div>
        </div>

        <!-- 当选择"患者无样可采"时显示备注输入框 -->
        <div v-if="weiShengWuForm.selectedOption === '2'" class="question-section">
          <div class="question-title">
            <span>请说明理由：</span>
          </div>
          <div class="question-content">
            <div class="content-item">
              <el-input
                v-model="weiShengWuForm.beiZhu"
                type="textarea"
                :rows="3"
                placeholder="请说明理由并做好病程记录备查"
                maxlength="500"
                show-word-limit
                clearable
              />
            </div>
          </div>
        </div>
      </div>
    </default-dialog>

    <!-- 撤销执行理由输入弹窗 -->
    <default-dialog
      :visible.sync="cancelExecuteDialog"
      title="撤销执行"
      width="500px"
      @confirm="handleCancelExecuteConfirm"
      @cancel="handleCancelExecuteCancel"
    >
      <div style="width: 100%">
        <div style="margin-bottom: 10px">撤销执行的理由：</div>
        <el-input
          v-model="cancelExecuteReason"
          type="textarea"
          :rows="3"
          placeholder="请输入撤销执行理由"
          maxlength="50"
          show-word-limit
        ></el-input>
        <div v-if="!cancelExecuteReason" style="color: red; font-size: 12px; margin-top: 5px">
          理由不能为空
        </div>
        <div
          v-if="cancelExecuteReason && cancelExecuteReason.length > 50"
          style="color: red; font-size: 12px; margin-top: 5px"
        >
          理由长度不能超过50
        </div>
      </div>
    </default-dialog>
    <!-- -->
    <clinical-decision-making
      :request="request"
      :visible.sync="visible"
      v-bind="isOptions"
      @dialogResult="dialogResult"
      @closeSelfPayingResult="closeSelfPayingResult"
    />
  </div>
</template>

<script>
import {
  checkJinRiCY,
  checkShuQianYz,
  checkYpSfzt,
  disSubmitYiZhu,
  getCityOptions,
  getCountyOptions,
  getCurrentDateTime,
  getInpatientOrders,
  getJingPeiConfigByZuHaoList,
  getKangJunYpGl,
  getShiFouYLSS,
  getGanRanZD,
  getLastSGTZ,
  getLiShiYyMb,
  getBinRenXXNoQuanXianByParam,
  getMaZuiYT,
  getNewWaiGouYP,
  getProvinceOptions,
  getUnCommitOrders,
  getYaoPinFJXX,
  getYaoPinJBSYFF,
  getYaoPinMbByLb,
  getYaoPinMbDetail,
  getYaoPinYzByTime,
  getYinShiFJXX,
  getZhiLiaoFJXX,
  getZhongXuanYaoPinList,
  getZhongYiBZXX,
  getZyKuaiDiDz,
  inpatientInit,
  saveLastSGTZ,
  saveYiZhu,
  saveYzBiaoTiGxh,
  saveZyKuaiDiDz,
  stopYiZhu,
  submitYZ,
  checkChuYuanLu,
  checkZhiXingBQ,
  linShiYzZx,
  saveAsYaoPinMb,
  getChuFangZD,
  getZhenDuanList,
  itemOfRuleEngine,
  triggerRuleByScenarioCodeVersion2,
  initQingJieSsGwYs,
  copyToLinShiYz,
  cheXiaoZxByYs,
  initWeiShengWuSjQkXx
} from '@/api/inpatient-order'
import { format } from 'date-fns'
import { mapState } from 'vuex'
import { EventBus } from '@/utils/event-bus'
import laboratoryMedicalAdvice from './components/laboratory-medical-advice/index.vue'
import medicalTechnology from './components/medical-technology/index.vue'
import orderSheet from './components/order-sheet/index.vue'
import OrderFooterAction from './components/OrderFooterAction.vue'
import DrawerTable from './components/DrawerTable.vue'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import SheBaoDialog from '@/components/Dialog/sheBaoDialog.vue'
import MultipleSheBaoDialog from '@/components/Dialog/MultipleSheBaoDialog.vue'
import { isArray, isPlainObject } from 'lodash'
import ChangWaiDialog from './components/ChangWaiDialog.vue'
import HealOrderTemplateDialog from './components/HealOrderTemplateDialog.vue'
import ComprehensiveOrderTemplateDialog from './components/ComprehensiveOrderTemplateDialog.vue'
import request from '@/utils/request'
import { ClinicalDecisionMaking } from 'wyyy-component'
import { title } from '@/settings'
import InPatientOrdersTips from './components/InPatientOrdersTips.vue'
import DiagnosticsSelector from '@/components/DiagnosticsSelector/index.vue'
import SecondaryPassword from '@/components/SecondaryPassword/index.vue'
import {
  createYiZhuSpanMethod,
  createGenericSpanMethod,
  shouldShowRepeatedField,
  getDisplayFieldValue
} from '@/utils/tableSpanMethod'

export default {
  name: 'InPatientOrders',
  components: {
    SheBaoDialog,
    MultipleSheBaoDialog,
    DrawerTable,
    OrderFooterAction,
    laboratoryMedicalAdvice,
    medicalTechnology,
    orderSheet,
    DefaultDialog,
    TableTag: () => import('./components/TableTag.vue'),
    TableLink: () => import('./components/TableLink.vue'),
    ChangWaiDialog,
    HealOrderTemplateDialog,
    ComprehensiveOrderTemplateDialog,
    ClinicalDecisionMaking,
    InPatientOrdersTips,
    DiagnosticsSelector,
    SecondaryPassword
  },
  data() {
    return {
      request,
      visible: false,
      // 组件配置数据
      isOptions: {
        biaoShiHao: 3178318, //标识号
        bingAnHao: '**********', //病案号
        changJingDM: 'cwsf_zyyz', //场景代码
        yingYongDM: null, //病案号
        yongHuID: null, //场景代码
        //临时数据
        linShiSJ: {
          Charges: [
            {
              guoJiaBM: '001204000010000-12040000100',
              xiangMuMC: '肌肉注射',
              shuLiang: 1
            }
          ]
        },
        linShiZD: {},
        shiFouMZ: false, //是否门诊
        isItemOfRuleEngine: true //  是否开启打开组件时调用检测是否使用该组件的接口
      },
      PayingResultData: [],
      tabActive: 'cq',
      linChuangLuJing: '',
      allOrders: false,
      stoppedOrders: true,
      copyLastTime: false,
      copyLastBingQu: false,
      leiBieFilter: '',
      feiYongLXFilter: '',
      columns: [],
      // 初始化信息
      inpatientInit: {},
      // 临时医嘱执行弹窗
      executeDialogVisible: false,
      selectedExecuteRows: [],
      executeTime: '',
      // 历史用药弹窗
      historyDrugDialog: false,
      historyDrugTitle: '',
      historyDrugType: 'lscq',
      historyDrugDateRange: ['', ''],
      historyDrugList: [],
      // 历次住院用药弹窗
      previousHospitalDialog: false,
      previousHospitalList: [],
      selectedHospitalRecord: null,
      // 虚拟药库
      virtualDrugDialog: false,
      virtualDrugTitle: '',
      virtualDrugInput: '',
      virtualDrugList: [],
      // 中成药分餐
      chinesePatentDrugDialog: false,
      chinesePatentDrugList: [],
      // 肠外营养套餐弹窗
      changWaiDialog: false,
      // 通用汤剂弹窗
      tongYongTJDialog: false,
      tongYongTJMuBan: [],
      tongYongTJMuBanTemp: [],
      tongYongTJMuBanMingXi: [],
      // 表格配置
      tableConfigDialog: false,
      biaoTiGXH: {},
      tableRefreshTimer: null, // 表格刷新防抖定时器
      tableConfigList: [
        { show: true, key: '组号' },
        { show: true, key: '医嘱状态' },
        { show: true, key: '医嘱类型' },
        { show: true, key: '开始日期' },
        { show: true, key: '开始时间' },
        { show: true, key: '医嘱名称' },
        { show: true, key: '组' },
        { show: true, key: '一次用量' },
        { show: true, key: '剂量单位' },
        { show: true, key: '用法' },
        { show: true, key: '频率' },
        { show: true, key: '用药执行时间' },
        { show: true, key: '规格/单价' },
        { show: true, key: '医师姓名' },
        { show: true, key: '类别' },
        { show: true, key: '费用类型' },
        { show: true, key: '自备' },
        { show: true, key: '今日临停' },
        { show: true, key: '特殊用法/备注说明' },
        { show: true, key: '执行病区' },
        { show: true, key: '数量/剂数' },
        { show: true, key: '持续天数' },
        { show: true, key: '用药天数' },
        { show: true, key: '单位' },
        { show: true, key: '收费次数' },
        { show: true, key: '预计出院' },
        { show: true, key: '是否代煎' },
        { show: true, key: '滴速' },
        { show: true, key: '结束时间' },
        { show: true, key: '手术通知单' },
        { show: true, key: '医嘱时间' },
        { show: true, key: '导入人员' },
        { show: true, key: '导入时间' },
        { show: true, key: '医嘱ID' },
        { show: true, key: '闭环' },
        { show: true, key: '不良反应上报' }
      ],
      // 个人模板
      personalDialog: false,
      muBanFilter: '',
      personalMuban: [],
      personalMuBanTemp: [],
      // 模板明细
      isDetail: false,
      selectedMuBanMC: '',
      personalMuBanMingXi: [],
      // 常用药模板
      changYongYaoDialog: false,
      changYongYaoList: [],
      // 治疗医嘱模板
      zhiLiaoMubanDialog: false,
      // 综合医嘱模板
      zongHeMubanDialog: false,
      // 保存为模板
      saveMBDialog: false,
      saveMBList: [],
      selectedMBRows: [],
      saveType: '1',
      templateName: '',
      // 医嘱列表
      yiZhuList: [],
      yiZhuListHeight: '100%',
      // 需要隐藏重复值的字段配置
      hideRepeatedFields: ['kaiShiSJ_rq'], // 可以添加更多字段
      // 当前点击行
      clickedRowIndex: null,
      // 新增弹窗
      orderDrawer: false,
      // 弹窗医嘱列表
      drawerYiZhuList: [],
      drawerClickedRowIndex: null,
      drawerSelectedRows: [],
      // 删除医嘱ID列表
      shanChuYZIDList: [],
      // 中选药品弹窗
      zhongXuanDialog: false,
      zhongXuanYaoPinList: [],
      zhongXuanYaoPinData: {},
      // 中医辨证弹窗
      zhongYiBZDialog: false,
      zhongYiBZData: {
        yaoPinMC: '',
        zhenDuanXX: [],
        zhengZhuangXX: []
      },
      zhongYiBZTableData: [],
      zhongYiBZResult: {
        zhongChengYaoZD: [], // 存储选中的诊断ID
        zhongChengYaoZZ: [] // 存储选中的症状ID
      },
      // 麻醉用途弹窗
      mzytDialog: false,
      mzytList: [],
      mzytValue: '',
      // 社保审批弹窗
      sheBaoDialog: false,
      displayList: [],
      sheBaoYaoPinData: null,
      dialogType: '',
      shangCiXdfw: '',
      // 治疗医嘱社保审批弹窗
      zhiLiaoSheBaoDialog: false,
      zhiLiaoYZMXWithSheBao: [],
      zhiLiaoYZMXWithoutSheBao: [],
      // 保存医嘱
      isSubmitting: false,
      // 是否计算体表面积药品
      isTBMJYaoPin: false,
      // 体表面积弹框
      tbmjDialog: false,
      TBMJ: 0,
      TBMJText: '',
      // 最近身高体重
      lastSGTZ: {
        shenGao: '',
        tiZhong: '',
        caoZuoZhe: '',
        ceLiangSJ: ''
      },
      // 草药代煎标志 0:不代煎 1:代煎 9:非草药
      cyDaiJian: '9',
      // 皮试医嘱（1=自动生成一条药品医嘱（皮试），0=不生成）
      piShiYz: '0',
      // 住院快递弹窗
      zykdDialog: false,
      zykdAddress: {
        shouHuoRenXM: '',
        lianXiDH: '',
        shengFen: '',
        chengShi: '',
        qu: '',
        lianXiDZ: '',
        xiuGaiSJ: '',
        xiuGaiYHID: '',
        beiZhu: ''
      },
      // 地区选择数据
      provinceOptions: [],
      cityOptions: [],
      countyOptions: [],
      // 处方药诊断弹窗
      chuFangZDDialog: false,
      chuFangZDList: [], // 处方诊断列表
      // 诊断列表弹窗
      zhenDuanListDialog: false,
      currentZhenDuanIndex: -1, // 当前选中的处方诊断索引
      zhenDuanListData: [], // 诊断列表数据
      zhenDuanFilter: '', // 诊断名称搜索
      icd: '', // 诊断ICD
      pageIndex: 1, // 当前页码
      pageSize: 20, // 每页条数
      total: 0, // 总条数
      isChuFangSelected: false, // 是否选择了处方诊断
      linShiTJData: [], //
      // 抗菌药物使用方法弹窗
      kangJunYpDialog: false,
      kangJunYpData: {
        shiYongFF: [], // 使用方法选项
        qieKouLB: [], // 切口类别选项
        yongFaFL: [], // 用法分类选项
        shuQianSY: [] // 术前使用选项
      },
      isGaoWeiYS: false, // 是否选择高危因素
      yiLeiSSList: [], // 一类手术列表
      kangJunYpForm: {
        shiYongFFValue: '', // 选中的使用方法
        qieKouLBValue: '', // 选中的切口类别
        yongFaFLValue: '', // 选中的用法分类（单选）
        shuQianSYValue: '', // 选中的术前使用
        shouShuMC: '' // 手术名称
      },
      keShiZRMC: '', // 科室主任名称
      keShiZRQMSJ: '', // 科室主任签名时间
      currentKangJunYpRow: null, // 当前处理的抗菌药物行数据
      currentKangJunYpDrugRow: null, // 当前处理的药品数据
      // 二次签名相关
      secondaryPasswordVisible: false, // 二次签名弹窗显示状态
      secondaryPasswordUserData: null, // 二次签名用户数据
      // 高危因素弹窗
      gaoWeiYSDialog: false,
      gaoWeiYSList: [
        {
          daiMa: '1',
          mingCheng: '手术范围大、手术时间长、污染机会增加'
        },
        {
          daiMa: '2',
          mingCheng: '手术涉及重要脏器，一旦发生感染将造成严重后果者，如头颅手术、心脏手术等'
        },
        {
          daiMa: '3',
          mingCheng: '异物植入手术，如人工心瓣膜植入、永久性心脏起搏器放置、人工关节置换等'
        },
        {
          daiMa: '4',
          mingCheng:
            '有感染高危因素如高龄、糖尿病、免疫功能低下（尤其是接受器官移植者）、营养不良等患者'
        }
      ],
      selectedGaoWeiYS: [], // 选中的高危因素
      // 微生物送检情况弹窗
      weiShengWuDialog: false,
      weiShengWuOptions: [
        {
          daiMa: '1',
          mingCheng: '患者有样可采，请开具微生物送检医嘱并采集条码，之后方可开药'
        },
        {
          daiMa: '2',
          mingCheng: '患者无样可采，请说明理由并做好病程记录备查'
        },
        {
          daiMa: '3',
          mingCheng: '已送检PCT/IL-6/G实验/GM试验，已在抗菌药物开具前完成采集（必须已刷采集条码）'
        }
      ],
      weiShengWuForm: {
        selectedOption: '',
        beiZhu: ''
      },

      // 新生儿抗生素预防使用问卷弹窗
      newbornQuestionnaireDialog: false,
      newbornQuestionnaireForm: {
        shiFouYWCG: '', // 是否有围产高危因素
        shiFouYLCBX: '', // 是否有临床表现
        weiChanGWYSS: [], // 围产高危因素选中的选项
        linChuangBX: [], // 临床表现选中的选项
        qiTaWCGW: '' // 其他，具体诊断
      },
      // 感染诊断选择弹窗
      ganRanZDSelectDialog: false,
      ganRanZDList: [],
      // 感染诊断库
      ganRanZDLibraryDialog: false,
      // 撤销执行弹窗
      cancelExecuteDialog: false,
      cancelExecuteReason: '',
      selectedCancelRows: []
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    curBrZhuanKeID() {
      return this.inpatientInit.ezyblbrVo?.zhuanKeID
    },
    ...mapState({
      curYSZhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      yiShengID: ({ user }) => user.yongHuID,
      initInfo: ({ patient }) => patient.patientInit,
      doctorInfo: ({ patient }) => patient.doctorInfo
    })
  },
  watch: {
    drawerYiZhuList(val) {
      this.orderDrawer = val.length > 0
      this.yiZhuListHeight = val.length > 0 ? '25%' : '100%'
      if (val.length === 0) {
        this.drawerClickedRowIndex = null
      }
    }
  },
  async mounted() {
    window.addEventListener('keydown', this.handleKeyPress)
    // 先移除可能存在的旧监听器，再注册新的监听器，防止重复注册
    EventBus.$off(`startYZLogic_${this.bingLiID}`)
    EventBus.$on(`startYZLogic_${this.bingLiID}`, this.yzLogic)
    await this.init()
    await this.getBingRenYZ()
    await this.getUncommitYZ()
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeyPress)
    EventBus.$off(`startYZLogic_${this.bingLiID}`, this.yzLogic)

    // 清理表格刷新定时器
    if (this.tableRefreshTimer) {
      clearTimeout(this.tableRefreshTimer)
      this.tableRefreshTimer = null
    }
  },
  methods: {
    async init() {
      try {
        // 界面初始化
        const res = await inpatientInit({ bingLiID: this.bingLiID })
        if (res.hasError === 0) {
          this.inpatientInit = res.data
          // 初始化表格配置
          if (this.inpatientInit?.biaoTiGXH?.geXingHuaNR) {
            try {
              const geXingHuaNR = JSON.parse(this.inpatientInit.biaoTiGXH.geXingHuaNR)
              this.tableConfigList = geXingHuaNR.filter(
                (config) => config.key !== '煎法' && config.key !== '选择'
              )
            } catch (parseError) {
              console.error('解析表格配置失败，使用默认配置：', parseError)
              this.resetTableConfigToDefault()
            }
          } else {
            // 如果没有保存的配置，使用默认配置
            this.resetTableConfigToDefault()
          }
          // 设置初始列显示
          this.setTableColumns()
        }
      } catch (error) {
        console.error('初始化失败：', error)
        // 初始化失败时也要设置默认表格配置
        this.resetTableConfigToDefault()
        this.setTableColumns()
      }
    },
    handleKeyPress(e) {
      // 过滤非Alt组合
      if (!e.altKey) return
      const keyMap = {
        t: 'submit',
        s: 'save',
        1: 'addGroup',
        2: 'add',
        3: 'delGroup',
        4: 'del',
        5: 'personal',
        6: 'common',
        7: 'heal',
        8: 'comprehensive',
        9: this.tabActive === 'cq' ? 'stop' : 'cancel',
        0: 'disSubmit',
        p: 'execute',
        m: 'nextDay'
      }
      const key = e.key.toLowerCase()
      if (keyMap[key]) {
        e.preventDefault()
        this.handleActionClicked(keyMap[key])
      }
    },
    // 医嘱列表合并
    handleSpanMethod({ row, column, rowIndex }) {
      // 使用通用的医嘱表格行合并方法
      const spanMethod = createYiZhuSpanMethod(this.yiZhuList)
      return spanMethod({ row, column, rowIndex })
    },
    // 行点击高亮
    handleRowClicked(row) {
      this.clickedRowIndex = this.yiZhuList.indexOf(row)
    },
    // 是否可选
    isRowSelectable(row) {
      return (
        (row.yanSe !== 'red' || row.zhuangTaiBZMC === '到期') &&
        (row.yiZhuLX === '0' || row.yiZhuLX === '1' || row.yiZhuLX === '2' || row.yiZhuLX === '98')
      )
    },
    // 动态绑定行类名
    tableRowClassName({ row, rowIndex }) {
      let className = ''
      if (rowIndex === this.clickedRowIndex) {
        className += 'selected-row '
      }
      switch (row.yanSe) {
        case 'blue':
          className += 'blue-row '
          break
        case 'red':
          className += 'red-row '
          break
        case 'black':
        default:
          className += ''
      }
      return className.trim()
    },
    async handleChangeTab(tab) {
      this.tabActive = tab.name
      this.clickedRowIndex = null
      this.drawerYiZhuList = []
      if (tab.name === 'cq' || tab.name === 'ls') {
        await this.getBingRenYZ()
        await this.getUncommitYZ()
      } else if (tab.name === 'hy') {
        this.$refs.hyyz.initPage()
      } else if (tab.name === 'yj') {
        this.$refs.yjjc.initPage()
      }
    },
    handleTableFilterChange(filters) {
      if (filters.leiBie) {
        this.leiBieFilter = filters.leiBie[0] || ''
      }
      if (filters.feiYongLX) {
        this.feiYongLXFilter = filters.feiYongLX[0] || ''
      }
      this.getBingRenYZ()
    },

    async getBingRenYZ() {
      /**
       * 获取病人医嘱
       * @param {string} params.bingLiID 病历ID
       * @param {string} params.yiZhuLB 医嘱类别：1=长期，2=临时，jcyy=检查用药(当作临时医嘱)
       * @param {string} params.zaiYong 是否在用：1=取在用 0=取所有
       * @param {string} params.tingZhi 是否显示停止医嘱：1=显示，0=不显示
       * @param {string} params.leiBie 类别：空=全部，1药品，0饮食，2治疗，3化验，4检查，98嘱托，99其他
       * @param {string} params.feiYongLX 费用类型：空=全部，0自费，1甲类，2乙类，99其他
       */
      this.yiZhuList = []
      await getInpatientOrders({
        bingLiID: this.bingLiID,
        yiZhuLB: this.tabActive === 'cq' ? '1' : '2',
        zaiYong: this.allOrders ? '0' : '1',
        tingZhi: this.stoppedOrders ? '1' : '0',
        leiBie: this.leiBieFilter,
        feiYongLX: this.feiYongLXFilter
      })
        .then((res) => {
          if (res.hasError === 0) {
            this.yiZhuList = res.data
            if (this.yiZhuList.length) {
              // 生成虚拟组号逻辑
              this.generateXuNiZH(this.yiZhuList)
            }
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    async getUncommitYZ() {
      /**
       * 获取未提交医嘱
       * @param {string} params.bingLiID 病历ID
       * @param {string} params.leiXing 医嘱类别：（cq=长期，ls=临时，jcyy=检查用药）
       * @param {string} params.shenQingDanID 申请单ID
       */
      await getUnCommitOrders({
        bingLiID: this.bingLiID,
        leiXing: this.tabActive,
        shenQingDanID: 0
      })
        .then((res) => {
          if (res.hasError === 0) {
            this.drawerYiZhuList = res.data
            if (this.drawerYiZhuList.length) {
              // 生成虚拟组号
              this.generateXuNiZH(this.drawerYiZhuList)
              // 确保bingQuID为字符串类型
              this.drawerYiZhuList.forEach((item) => {
                item.bingQuID = item.bingQuID.toString()
              })
            }
          }
        })
        .catch((error) => {
          this.drawerYiZhuList = []
        })
    },
    // 生成虚拟组号
    generateXuNiZH(yiZhuList) {
      // 分离有组号和无组号的医嘱
      const withZuHao = yiZhuList.filter(
        (item) => item.zuHao && item.zuHao !== null && item.zuHao !== ''
      )
      const withoutZuHao = yiZhuList.filter(
        (item) => !item.zuHao || item.zuHao === null || item.zuHao === ''
      )

      // 处理有组号的医嘱：获取所有不重复的组号
      const zuHaoList = [...new Set(withZuHao.map((item) => item.zuHao))]

      // 生成组号和虚拟组号的映射关系
      const zuHaoMap = {}
      zuHaoList.forEach((zuHao, index) => {
        zuHaoMap[zuHao] = (index + 1).toString()
      })

      // 为有组号的医嘱添加虚拟组号
      withZuHao.forEach((item) => {
        item.xuNiZH = zuHaoMap[item.zuHao]
      })

      // 为无组号的医嘱按顺序分配虚拟组号
      let nextXuNiZH = zuHaoList.length + 1
      withoutZuHao.forEach((item) => {
        item.xuNiZH = nextXuNiZH.toString()
        nextXuNiZH++
      })

      // 生成zuHaoTXT显示符号
      this.generateZuHaoTXT(yiZhuList)
    },
    // 生成组号显示符号
    generateZuHaoTXT(yiZhuList) {
      yiZhuList.forEach((item, index) => {
        // 对于没有原始组号的医嘱，不显示组号符号
        if (!item.zuHao) {
          item.zuHaoTXT = ''
          return
        }

        const currentZuHao = item.zuHao
        const nextItem = yiZhuList[index + 1]
        const prevItem = yiZhuList[index - 1]
        const nextZuHao = nextItem?.zuHao
        const prevZuHao = prevItem?.zuHao

        let zuHaoTXT = ''
        if (currentZuHao === prevZuHao && currentZuHao === nextZuHao) {
          // 当前项在组的中间
          zuHaoTXT = '|'
        } else if (currentZuHao === nextZuHao && currentZuHao !== prevZuHao) {
          // 当前项是组的第一项
          zuHaoTXT = '┐'
        } else if (currentZuHao !== nextZuHao && currentZuHao === prevZuHao) {
          // 当前项是组的最后一项
          zuHaoTXT = '┘'
        }
        item.zuHaoTXT = zuHaoTXT
      })
    },
    // 所有医嘱
    handleAllOrdersChanged() {
      this.getBingRenYZ()
      this.getUncommitYZ()
    },
    // 停止医嘱
    handleStoppedOrdersChanged() {
      this.getBingRenYZ()
      this.getUncommitYZ()
    },
    // 点击页面按钮
    async handleActionClicked(action) {
      switch (action) {
        case 'refresh':
          await this.init()
          await this.getBingRenYZ()
          await this.getUncommitYZ()
          break
        case 'stop': // 立即停
          await this.stopYZ('1')
          break
        case 'disSubmit': // 撤回
          await this.disSubmitYZ()
          break
        case 'execute': // 执行后停
          await this.stopYZ('2')
          break
        case 'linShiExecute': // 临时医嘱执行
          await this.linShiExecute()
          break
        case 'cancelExecute': // 撤销执行
          await this.cancelExecute()
          break
        case 'nextDay': // 次日停
          await this.stopYZ('3')
          break
        case 'cancel': // 撤销
          await this.stopYZ()
          break
        case 'drugInfo': // 药品说明书
          this.openDrugInfo()
          break
        case 'chinesePatentDrug': // 中成药分餐
          this.chinesePatentDrugDialog = true
          getYaoPinMbByLb({
            bingLiID: this.bingLiID,
            muBanLB: 'zcyfc',
            yiZhuLX: this.tabActive
          }).then((res) => {
            if (res.hasError === 0) {
              this.chinesePatentDrugList = res.data
            }
          })
          break
        case 'changWaiDialog': // 肠外营养套餐
          this.changWaiDialog = true
          break
        case 'tableConfig': // 表格配置
          this.tableConfigDialog = true
          break
        case 'submit': // 审核提交
          await this.saveYz('submit')
          break
        case 'save': // 保存
          await this.saveYz('save')
          break
        case 'addGroup': // 新增组
          await this.addGroup()
          break
        case 'add': // 新增项
          await this.add()
          break
        case 'insert': // 插入项
          await this.insert()
          break
        case 'delGroup': // 删除组
          this.delGroup()
          break
        case 'del': // 删除项
          this.del()
          break
        case 'personal': // 个人模板
          this.isDetail = false
          this.muBanFilter = ''
          this.personalDialog = true
          getYaoPinMbByLb({
            bingLiID: this.bingLiID,
            muBanLB: '1',
            yiZhuLX: this.tabActive
          }).then((res) => {
            if (res.hasError === 0) {
              this.personalMuban = this.personalMuBanTemp = res.data
            }
          })
          break
        case 'common': // 常用药
          this.changYongYaoDialog = true
          getYaoPinMbByLb({
            bingLiID: this.bingLiID,
            muBanLB: '0',
            yiZhuLX: this.tabActive
          }).then((res) => {
            if (res.hasError === 0) {
              this.changYongYaoList = res.data
            }
          })
          break
        case 'tongYongTJ':
          this.isDetail = false
          this.muBanFilter = ''
          this.tongYongTJDialog = true
          getYaoPinMbByLb({
            bingLiID: this.bingLiID,
            muBanLB: 'tytj',
            yiZhuLX: this.tabActive
          }).then((res) => {
            if (res.hasError === 0) {
              this.tongYongTJMuBan = this.tongYongTJMuBanTemp = res.data
            }
          })
          break
        case 'heal': // 治疗医嘱模板
          this.zhiLiaoMubanDialog = true

          break
        case 'comprehensive': // 综合医嘱模板
          this.zongHeMubanDialog = true
          break
        case 'saveMB': // 保存模板
          getYaoPinYzByTime({ bingLiID: this.bingLiID }).then((res) => {
            if (res.hasError === 0) {
              this.saveMBDialog = true
              this.saveMBList = res.data
            }
          })
          break
        default:
          break
      }
    },
    drawerRowClicked({ row }) {
      this.drawerClickedRowIndex = this.drawerYiZhuList.indexOf(row)
    },
    drawerSelectionChange(selection) {
      this.drawerSelectedRows = selection
    },
    // 处理DrawerTable请求当前时间的事件
    handleRequestCurrentTime(callback) {
      this.getCurrentTime().then((currentTime) => {
        if (callback && typeof callback === 'function') {
          callback(currentTime)
        }
      })
    },
    // 处理DrawerTable请求新增项的事件
    handleRequestAddItem() {
      this.handleActionClicked('add')
    },
    // 获取当前时间
    async getCurrentTime() {
      const res = await getCurrentDateTime()
      if (res.hasError === 0) {
        return res.data
      }
    },
    // 新增组
    async addGroup() {
      // 检查最后一条医嘱是否已填写名称
      const index = this.drawerYiZhuList.length - 1
      const lastOrder = this.drawerYiZhuList[index]
      if (lastOrder && !lastOrder.mingCheng) {
        this.$message.warning('请先完善上一组医嘱名称')
        return
      }
      // 创建新组对象
      const newGroup = {}
      // 处理勾选了复制时间和病区信息
      if (lastOrder) {
        if (this.copyLastTime) {
          newGroup.kaiShiSJ = lastOrder.kaiShiSJ
          newGroup.jieShuSJ = lastOrder.jieShuSJ
        } else {
          newGroup.kaiShiSJ = await this.getCurrentTime()
          newGroup.jieShuSJ = ''
        }
        if (this.copyLastBingQu) {
          newGroup.bingQuID = lastOrder.bingQuID
        } else {
          newGroup.bingQuID = this.inpatientInit?.ezyblbrVo?.bingQuID.toString() || ''
        }
        newGroup.yiZhuLX = lastOrder.yiZhuLX || this.tabActive
        newGroup.leiBie = lastOrder.leiBie || ''
        // 出院带药处理
        if (newGroup.yiZhuLX === 'cydy') {
          newGroup.jinRiCY = lastOrder.jinRiCY || '2'
        }
        // 草药带药且医生为中医科时处理
        if (newGroup.yiZhuLX === 'zcydy' && String(this.curYSZhuanKeID) === '33') {
          newGroup.jinRiCY = lastOrder.jinRiCY || '1'
          newGroup.zanShiBQ = lastOrder.zanShiBQ || '4'
        }
        // 设置草药和草药带药特殊组号
        if (newGroup.yiZhuLX === 'cy' || newGroup.yiZhuLX === 'zcydy') {
          newGroup.xuNiZH = (parseInt(lastOrder.xuNiZH) + 1).toString() + '-' + '1'
        } else {
          newGroup.xuNiZH = (parseInt(lastOrder.xuNiZH) + 1).toString()
        }
      } else {
        // 第一条医嘱
        newGroup.xuNiZH = '1'
        newGroup.yiZhuLX = this.tabActive
        newGroup.kaiShiSJ = await this.getCurrentTime()
        newGroup.bingQuID = this.inpatientInit?.ezyblbrVo?.bingQuID.toString() || ''
      }
      // 添加新组
      this.drawerYiZhuList.push(newGroup)
      // 更新选中行
      this.drawerClickedRowIndex = index + 1
      // 重新排序组号
      this.reorderZuHao()
      // 自动聚焦到新增的医嘱名称字段，使用延迟确保DOM完全渲染
      setTimeout(() => {
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
        if (drawerTableRef) {
          // 新增组时聚焦到名称
          drawerTableRef.initializeFocus()
        }
      }, 200)
    },
    // 新增项
    async add() {
      // 获取当前点击行索引
      const index =
        this.drawerClickedRowIndex !== null
          ? this.drawerClickedRowIndex
          : this.drawerYiZhuList.length - 1
      // 创建新医嘱对象
      let newOrder = {}
      let prevOrder = this.drawerYiZhuList[index]
      if (!prevOrder) {
        // 第一条医嘱
        newOrder = {
          xuNiZH: '1',
          yiZhuLX: this.tabActive,
          kaiShiSJ: await this.getCurrentTime(),
          bingQuID: this.inpatientInit?.ezyblbrVo?.bingQuID.toString() || ''
        }
      } else {
        // 复制上一条医嘱的部分属性
        newOrder = {
          xuNiZH: prevOrder.xuNiZH,
          zuHao: prevOrder.zuHao,
          yiZhuLX: prevOrder.yiZhuLX,
          leiBie: prevOrder.leiBie,
          kaiShiSJ: prevOrder.kaiShiSJ,
          zhiXingFF: prevOrder.zhiXingFF || '',
          zhiXingPL: prevOrder.zhiXingPL || 'qd',
          geiYaoSJ: prevOrder.geiYaoSJ || '',
          bingQuID: prevOrder.bingQuID || this.inpatientInit?.ezyblbrVo?.bingQuID.toString() || '',
          jieShuSJ: prevOrder.jieShuSJ || '',
          tongZhiDanID: prevOrder.tongZhiDanID || ''
        }
        // TODO:时间针频率？
        // if (WRT_config.yz_sz.zkid == 49 && this.tabActive === 'ls') {
        //   newOrder.SJZZXPL = lists.SJZZXPL || ''
        // }
        if (newOrder.yiZhuLX === 'cy' || newOrder.yiZhuLX === 'zcydy') {
          newOrder.shouFeiSL = prevOrder.shouFeiSL || prevOrder.shouFeiCS || '7'
        } else if (newOrder.yiZhuLX === 'cydy') {
          newOrder.jinRiCY = prevOrder.jinRiCY || '2'
        }
        if (newOrder.yiZhuLX === 'zcydy' && String(this.curYSZhuanKeID) === '33') {
          newOrder.jinRiCY = prevOrder.jinRiCY || '1'
          newOrder.zanShiBQ = prevOrder.zanShiBQ || '4'
        }
      }
      // 插入新医嘱
      this.drawerYiZhuList.splice(index + 1, 0, newOrder)
      // 更新选中行
      this.drawerClickedRowIndex = index + 1
      // 重新排序组号
      this.reorderZuHao()
      // 自动聚焦到新增的医嘱名称字段，使用延迟确保DOM完全渲染
      setTimeout(() => {
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
        if (drawerTableRef) {
          // 新增组时聚焦到名称
          drawerTableRef.initializeFocus()
        }
      }, 200)
    },
    // 插入项
    async insert() {
      // 获取当前点击行索引
      const index =
        this.drawerClickedRowIndex !== null
          ? this.drawerClickedRowIndex
          : this.drawerYiZhuList.length - 1
      // 获取当前行医嘱
      const currentOrder = this.drawerYiZhuList[index]
      if (!currentOrder) return
      // 判断是否为当前组的第一条医嘱
      const isFirstInGroup =
        index === 0 || this.drawerYiZhuList[index - 1]?.xuNiZH !== currentOrder.xuNiZH
      // 判断是否为药品或嘱托
      const isLeiBie1Or2 = currentOrder.leiBie === '1' || currentOrder.leiBie === '2'
      if (isLeiBie1Or2) {
        // 直接作为新组插入
        await this.insertAsNewGroup(currentOrder, index)
      } else if (isFirstInGroup) {
        // 是第一条弹出确认框
        try {
          await this.$confirm(
            `是否作为${currentOrder.mingCheng ? currentOrder.mingCheng : '当前组'}的项插入？`,
            '插入提示',
            {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'info'
            }
          )
          // 点击是，作为当前组新项插入
          await this.insertAsNewItem(currentOrder, index)
        } catch {
          // 点击否，作为新组插入
          await this.insertAsNewGroup(currentOrder, index)
        }
      } else {
        // 不是第一条，直接作为当前组新项插入
        await this.insertAsNewItem(currentOrder, index)
      }
      // 更新选中行
      this.drawerClickedRowIndex = index
      // 重新排序组号
      this.reorderZuHao()
    },
    async insertAsNewItem(currentOrder, index) {
      const newOrder = {
        xuNiZH: currentOrder.xuNiZH,
        zuHao: currentOrder.zuHao,
        yiZhuLX: currentOrder.yiZhuLX,
        kaiShiSJ: currentOrder.kaiShiSJ ? currentOrder.kaiShiSJ : await this.getCurrentTime(),
        jieShuSJ: currentOrder.jieShuSJ || '',
        leiBie: currentOrder.leiBie,
        bingQuID: currentOrder.bingQuID
          ? currentOrder.bingQuID
          : this.inpatientInit?.ezyblbrVo?.bingQuID.toString() || '',
        zhiXingFF: currentOrder.zhiXingFF || '',
        zhiXingPL: currentOrder.zhiXingPL || '',
        geiYaoSJ: currentOrder.geiYaoSJ || '',
        tongZhiDanID: currentOrder.tongZhiDanID || ''
      }
      if (newOrder.yiZhuLX === 'cydy') {
        newOrder.jinRiCY = currentOrder.jinRiCY || '2'
        newOrder.zanShiBQ = currentOrder.zanShiBQ || '2'
      }
      if (newOrder.yiZhuLX === 'zcydy' && String(this.curYSZhuanKeID) === '33') {
        newOrder.jinRiCY = currentOrder.jinRiCY || '1'
        newOrder.zanShiBQ = currentOrder.zanShiBQ || '4'
      }
      if (newOrder.yiZhuLX === 'cy' || newOrder.yiZhuLX === 'zcydy') {
        newOrder.shouFeiSL = currentOrder.shouFeiSL || currentOrder.shouFeiCS || '7'
      }
      // TODO: 时间针频率

      this.drawerYiZhuList.splice(index, 0, newOrder)
    },
    async insertAsNewGroup(currentOrder, index) {
      // 获取当前最大组号
      const maxZuHao = Math.max(
        ...this.drawerYiZhuList.map((item) => {
          const num = item.xuNiZH.split('-')[0] // 处理可能的 "1-1" 格式
          return parseInt(num)
        })
      )
      const newGroup = {
        xuNiZH: (maxZuHao + 1).toString(),
        yiZhuLX: currentOrder.yiZhuLX || this.tabActive,
        leiBie: currentOrder.leiBie,
        kaiShiSJ: this.copyLastTime ? currentOrder.kaiShiSJ : await this.getCurrentTime(),
        bingQuID: this.copyLastBingQu
          ? currentOrder.bingQuID
          : this.inpatientInit?.ezyblbrVo?.bingQuID.toString() || ''
      }
      if (newGroup.yiZhuLX === 'cy' || newGroup.yiZhuLX === 'zcydy') {
        newGroup.shouFeiSL = currentOrder.shouFeiSL || currentOrder.shouFeiCS || '7'
      }
      this.drawerYiZhuList.splice(index, 0, newGroup)
    },
    // 删除组
    delGroup() {
      // 获取当前点击行索引
      const index =
        this.drawerClickedRowIndex !== null
          ? this.drawerClickedRowIndex
          : this.drawerYiZhuList.length - 1
      // 获取当前行医嘱
      const currentOrder = this.drawerYiZhuList[index]
      if (!currentOrder) return
      // 获取当前组号
      const currentZuHao = currentOrder.xuNiZH.split('-')[0]

      // 收集已保存医嘱ID,将ID添加到删除列表中
      const deletingOrders = this.drawerYiZhuList.filter(
        (item) => item.xuNiZH.split('-')[0] === currentZuHao
      )
      deletingOrders.forEach((item) => {
        if (item.yiZhuID) {
          this.shanChuYZIDList.push(item.yiZhuID)
        }
      })

      // 删除相同组号的所有医嘱
      this.drawerYiZhuList = this.drawerYiZhuList.filter(
        (item) => item.xuNiZH.split('-')[0] !== currentZuHao
      )
      // 重置选中行索引
      this.drawerClickedRowIndex = null
      // 重新排序组号
      this.reorderZuHao()
    },
    // 删除项
    del() {
      // 获取当前点击行索引
      let index =
        this.drawerClickedRowIndex !== null
          ? this.drawerClickedRowIndex
          : this.drawerYiZhuList.length - 1
      // 获取要删除的医嘱
      const currentOrder = this.drawerYiZhuList[index]
      if (!currentOrder) return

      // 如果是已保存的医嘱，将ID添加到删除列表中
      if (currentOrder.yiZhuID) {
        this.shanChuYZIDList.push(currentOrder.yiZhuID)
      }

      // 从数组中删除该条医嘱
      this.drawerYiZhuList.splice(index, 1)
      // 更新选中行索引
      if (this.drawerYiZhuList.length > 0) {
        if (index === 0) {
          this.drawerClickedRowIndex = 0
        } else {
          this.drawerClickedRowIndex = index - 1
        }
      } else {
        this.drawerClickedRowIndex = null
      }
      // 重新处理组号
      this.reorderZuHao()
    },
    // 重新处理组号
    reorderZuHao() {
      if (this.drawerYiZhuList.length === 0) return

      // 获取所有不同的虚拟组号前缀(不包含"-"后面的部分)
      const groupPrefixes = []
      const groupItems = {}

      // 收集所有组号前缀和对应的项，保持原有顺序
      this.drawerYiZhuList.forEach((item) => {
        const isSpecialType = item.yiZhuLX === 'cy' || item.yiZhuLX === 'zcydy'
        let prefix = item.xuNiZH

        if (isSpecialType && item.xuNiZH && item.xuNiZH.includes('-')) {
          prefix = item.xuNiZH.split('-')[0]
        }

        // 只收集未收集的组号，保持它们首次出现的顺序
        if (!groupPrefixes.includes(prefix)) {
          groupPrefixes.push(prefix)
          groupItems[prefix] = []
        }
        groupItems[prefix].push(item)
      })

      // 按照原有顺序重新编号
      groupPrefixes.forEach((oldPrefix, index) => {
        const newGroupNum = (index + 1).toString()
        const items = groupItems[oldPrefix]

        items.forEach((item, itemIndex) => {
          if (item.yiZhuLX === 'cy' || item.yiZhuLX === 'zcydy') {
            item.xuNiZH = `${newGroupNum}-${itemIndex + 1}`
          } else {
            item.xuNiZH = newGroupNum
          }
        })
      })

      // 根据重新编号后的xuNiZH设置zuHaoTXT
      this.drawerYiZhuList.forEach((item, index) => {
        const currentPrefix = item.xuNiZH.split('-')[0]
        const nextItem = this.drawerYiZhuList[index + 1]
        const prevItem = this.drawerYiZhuList[index - 1]
        const nextPrefix = nextItem?.xuNiZH.split('-')[0]
        const prevPrefix = prevItem?.xuNiZH.split('-')[0]
        let zuHaoTXT = ''
        if (currentPrefix === prevPrefix && currentPrefix === nextPrefix) {
          // 当前项在组的中间
          zuHaoTXT = '|'
        } else if (currentPrefix === nextPrefix && currentPrefix !== prevPrefix) {
          // 当前项是组的第一项
          zuHaoTXT = '┐'
        } else if (currentPrefix !== nextPrefix && currentPrefix === prevPrefix) {
          // 当前项是组的最后一项
          zuHaoTXT = '┘'
        }
        this.$set(item, 'zuHaoTXT', zuHaoTXT)
      })
    },
    // 停止类型（仅用于长期医嘱，1=立即停，2=执行后停，3=次日停）
    async stopYZ(tingZhiLX = null) {
      try {
        // 获取选中的医嘱行
        let selectedRows = []
        // 确定停止类型名称
        let tingZhiName
        if (this.tabActive === 'ls') {
          tingZhiName = '撤销'
          selectedRows = this.$refs.tempTable.selection
        } else {
          tingZhiName = tingZhiLX === '1' ? '立即停' : tingZhiLX === '2' ? '执行后停' : '次日停'
          selectedRows = this.$refs.standingTable.selection
        }

        // 1. 基本检查 - 是否有选中医嘱
        if (selectedRows.length === 0) {
          await this.$confirm(`请选择要${tingZhiName}的医嘱`, '提示信息', {
            type: 'info'
          })
          return
        }

        // 2. 医嘱类型和状态检查
        let hasDaiyao = false // 是否包含出院带药或草药带药
        let zuHaoList = [] // 组号列表(用于检查药品状态)

        for (const item of selectedRows) {
          // 检查是否为出院带药或草药带药
          if (item.yiZhuLX === 'cydy' || item.yiZhuLX === 'zcydy') {
            hasDaiyao = true
          }

          // 保存药品组号
          if (item.leiBie === '3' && item.zuHao) {
            zuHaoList.push(item.zuHao)
          }

          // 检查未导出医嘱
          if (item.yanSe === 'blue') {
            await this.$confirm(`未导出医嘱不允许${tingZhiName}`, '提示信息', {
              type: 'info'
            })
            return
          }
        }

        // 3. 长期临时医嘱处理不同
        if (this.tabActive === 'cq' && tingZhiLX === '1' && zuHaoList.length > 0) {
          // 检查静配配置
          try {
            const peiyaoRes = await getJingPeiConfigByZuHaoList(zuHaoList)
            if (peiyaoRes.hasError === 0 && peiyaoRes.data.length > 0) {
              // 需确认是否立即停
              const peiyaoList = peiyaoRes.data.join('</br>')
              await this.$confirm(
                `请确认是否立即停。</br></br>${peiyaoList}</br></br>选择是表示立即停，选择否表示不停`,
                '提示信息',
                {
                  dangerouslyUseHTMLString: true,
                  distinguishCancelAndClose: true,
                  confirmButtonText: '是',
                  cancelButtonText: '否'
                }
              )
            }
          } catch (error) {
            if (error !== 'cancel') {
              throw error
            } else {
              return // 用户选择不停
            }
          }
        }

        // 4. 检查出院带药收费状态
        if (hasDaiyao && zuHaoList.length > 0) {
          try {
            const ypStatusRes = await checkYpSfzt({
              zuHaoList,
              bingLiID: this.bingLiID
            })

            if (ypStatusRes.hasError === 0 && ypStatusRes.message) {
              // 需确认继续停止
              await this.$confirm(ypStatusRes.message, '提示信息', {
                type: 'info'
              })
            }
          } catch (error) {
            if (error !== 'cancel') {
              throw error
            }
          }
        }

        // 5. 特殊医嘱检查 - 微泵维持类医嘱特殊处理
        const hasMicropump = selectedRows.some(
          (item) => item.zhiXingFF === 'iv-vp' && item.zhiXingPL === '维持'
        )

        if (hasMicropump && this.tabActive === 'cq') {
          // 如果包含微泵类医嘱，需选择立即停还是执行后停
          try {
            await this.$confirm('停止内容包含微泵类医嘱，请选择"立即停"/"执行后停"', '提示信息', {
              distinguishCancelAndClose: true,
              confirmButtonText: '立即停',
              cancelButtonText: '执行后停'
            })
            // 用户选择立即停
            tingZhiLX = '1'
          } catch (error) {
            if (error === 'cancel') {
              // 用户选择执行后停
              tingZhiLX = '2'
            } else {
              throw error // 其他错误继续抛出
            }
          }
        }

        // 分离有组号和无组号的医嘱
        const rowsWithZuHao = selectedRows.filter((row) => row.zuHao)
        const rowsWithoutZuHao = selectedRows.filter((row) => !row.zuHao)

        // 有组号时找出所有相同组号的医嘱
        const selectedZuHao = [...new Set(rowsWithZuHao.map((row) => row.zuHao))]
        const sameZuHaoRows = this.yiZhuList.filter((row) => selectedZuHao.includes(row.zuHao))

        // 合并所有选择医嘱
        const allStopRows = [...sameZuHaoRows, ...rowsWithoutZuHao]
        const yiZhuList = allStopRows.map((i) => i.val).toString()
        const yiZhuListName = allStopRows.map((i) => i.mingCheng).toString()

        // 6. 最终确认
        await this.$confirm(
          `确定要${tingZhiName}<span style="color: #356ac5">【${yiZhuListName}】</span>吗？`,
          `${tingZhiName}提示`,
          {
            type: 'info',
            dangerouslyUseHTMLString: true
          }
        )

        // 7. 调用停止接口
        const res = await stopYiZhu({
          bingLiID: this.bingLiID,
          yiZhuList: yiZhuList,
          leiBie: this.tabActive === 'cq' ? '1' : '2', // 1-长期医嘱，2-临时医嘱
          tingZhiLX: this.tabActive === 'ls' ? null : tingZhiLX
        })

        if (res.hasError === 0) {
          this.$message.success(`${tingZhiName}医嘱成功`)
          await this.getBingRenYZ()
          await this.getUncommitYZ()
        } else {
          throw new Error(res.message || `${tingZhiName}医嘱失败`)
        }
      } catch (error) {
        // 8. 错误处理
        if (error?.message !== 'cancel' && error !== 'cancel') {
          this.$message.error(error?.message || '停止医嘱出错')
        }
        console.error('停止医嘱出错:', error)
      }
    },
    // 撤回医嘱
    async disSubmitYZ() {
      try {
        // 1. 获取选中的医嘱
        const selectedRows =
          this.tabActive === 'ls'
            ? this.$refs.tempTable.selection
            : this.$refs.standingTable.selection

        // 2. 是否有选中医嘱
        if (selectedRows.length === 0) {
          await this.$confirm('请选择要撤回医嘱', '提示信息', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return
        }

        // 3. 验证医嘱状态并收集val
        const yiZhuList = []
        for (const item of selectedRows) {
          // 只允许撤回未导出(蓝色)的医嘱
          if (item.yanSe !== 'blue') {
            await this.$confirm(
              item.yanSe === 'red' ? `${item.zhuangTaiBZMC}医嘱不允许撤回` : '已导出医嘱不允许撤回',
              '提示信息',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              }
            )
            return
          }
          // 收集医嘱的val
          if (item.val) {
            yiZhuList.push(item.val)
          }
        }
        if (yiZhuList.length === 0) {
          return
        }

        // 4. 确认撤回
        try {
          await this.$confirm('是否确认撤回？', '提示信息', {
            type: 'warning',
            distinguishCancelAndClose: true
          })
        } catch (error) {
          return // 用户取消操作
        }

        // 5. 调用撤回接口
        const res = await disSubmitYiZhu({
          bingLiID: this.bingLiID,
          yiZhuList: yiZhuList.join(',')
        })

        // 6. 处理结果
        if (res.hasError === 0) {
          this.$message.success('撤回成功')
          // 刷新医嘱列表
          await Promise.all([this.getBingRenYZ(), this.getUncommitYZ()])
        } else if (res.message) {
          this.$message.error(res.message)
        }
      } catch (error) {
        console.error('撤回医嘱出错:', error)
        this.$message.error('撤回医嘱失败')
      }
    },
    // 临时医嘱执行
    async linShiExecute() {
      try {
        // 1. 获取选中的临时医嘱
        const selectedRows = this.$refs.tempTable.selection

        // 2. 验证是否选中医嘱
        if (selectedRows.length === 0) {
          await this.$confirm('至少选中一条医嘱', '提示窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return
        }

        // 3. 验证医嘱状态（必须是已导出的医嘱）
        const invalidOrder = selectedRows.find((item) => item.yanSe !== 'black')
        if (invalidOrder) {
          await this.$confirm('请选择要已导出医嘱', '提示窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return
        }

        // 4. 弹出执行时间选择窗口
        this.selectedExecuteRows = selectedRows
        this.executeTime = await this.getCurrentTime()
        this.executeDialogVisible = true
      } catch (error) {
        console.error('临时医嘱执行出错:', error)
        this.$message.error(error.message || '医嘱执行失败')
      }
    },
    // 执行弹窗确认
    async handleExecuteConfirm() {
      try {
        // 1. 验证执行时间
        if (!this.executeTime) {
          this.$message.warning('请选择执行时间')
          return
        }

        // 2. 获取医嘱ID列表
        const yiZhuList = this.selectedExecuteRows.map((item) => item.val).join(',')

        // 3. 调用执行接口
        const res = await linShiYzZx({
          yiZhuList,
          bingLiID: this.bingLiID,
          zhuYuanID: this.inpatientInit?.inPatientVo?.zhuYuanID,
          bingQuID: this.inpatientInit?.ezyblbrVo?.bingQuID,
          zhuanKeID: this.curYSZhuanKeID,
          zhiXingSJ: this.executeTime
        })

        // 4. 处理响应
        if (res.hasError === 0) {
          this.$message.success('医嘱执行成功')
          // 刷新医嘱列表
          await this.getBingRenYZ()
          await this.refresh_init()
        }
      } catch (error) {
        console.error('执行医嘱确认出错:', error)
        this.$message.error(error.message || '医嘱执行失败')
      }
    },
    // 执行弹窗取消
    handleExecuteDialogClose() {
      this.executeTime = ''
      this.selectedExecuteRows = []
    },
    // 撤销执行
    async cancelExecute() {
      try {
        // 1. 获取选中的医嘱
        const selectedRows = this.$refs.tempTable.selection

        // 2. 验证是否选中医嘱
        if (selectedRows.length === 0) {
          await this.$confirm('至少选中一条医嘱', '提示窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return
        }

        // 3. 验证医嘱状态（必须是已执行的医嘱）
        const invalidOrder = selectedRows.find((item) => item.zhuangTaiBZMC !== '已执行')
        if (invalidOrder) {
          await this.$confirm('请选择已执行医嘱', '提示窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return
        }

        // 4. 保存选中的医嘱并弹出理由输入框
        this.selectedCancelRows = selectedRows
        this.cancelExecuteReason = ''
        this.cancelExecuteDialog = true
      } catch (error) {
        console.error('撤销执行出错:', error)
        this.$message.error(error.message || '撤销执行失败')
      }
    },
    // 保存医嘱
    async saveYz(type) {
      if (this.isSubmitting) {
        return
      }

      try {
        this.isSubmitting = true

        // 2: 获取当前时间
        const dateNow = await this.getCurrentTime()

        // 3: 验证药品医嘱、病区入院
        if (!(await this.validateSpecialConditions())) {
          return
        }

        // 4: 初始数据结构
        const orderData = {
          arr: [], // 医嘱列表
          ganRanZD: '', // 感染诊断
          teShuYWBAs: [] // 特殊药物备案
        }

        // 5: 验证并处理每个医嘱
        for (const item of this.drawerYiZhuList) {
          if (!item.mingCheng) continue
          // for( let i in item){
          //   if(item[i] === null){
          //     item[i] = ''
          //   }
          // }
          // 5.1: 验证今日临停
          if (!(await this.validateJinRiLingTing(item))) {
            return
          }
          // 5.2: 验证草药用量
          if (!(await this.validateCaoYaoYongLiang(item))) {
            return
          }
          // 5.3: 组合医嘱检查
          if (item.zuHaoTXT) {
            if (!(await this.validateGroupOrders(item))) {
              return
            }
          } else {
            // 5.4: 单个医嘱出院带药数量检查
            if (!(await this.validateSingleOrderDrugQuantity(item))) {
              return
            }
          }
          // 5.5 抗菌药物使用方法需要吗

          // 5.6 特殊病备案需要吗

          // 5.7: 验证药品附加信息
          if (!(await this.validateDrugAttributes(item))) {
            return
          }
          // 5.8: 验证医嘱类型
          if (!(await this.validateOrderType(item))) {
            return
          }

          // 5.10: 验证特殊用法
          if (!(await this.validateSpecialUsage(item))) {
            return
          }
          // 5.11: 验证出院带药选项
          if (!(await this.validateDischargeMedication(item))) {
            return
          }
          // 5.12: 验证必填字段
          if (!(await this.validateRequiredFields(item))) {
            return
          }
          // 5.13: 验证开始时间
          if (!(await this.validateStartTime(item, dateNow))) {
            return
          }
          // 5.14: 构建保存数据
          const saveData = this.buildSaveData(item)
          orderData.arr.push(saveData)
        }

        // 6: 规则引擎校验
        const guiZe = await this.buildRuleEngineData(orderData.arr, dateNow)
        if (guiZe && guiZe.type === true) {
          orderData.arr = guiZe.arr
        } else if (guiZe) {
          return
        }
        // 7: 校验执行病区
        const bingQuIDs = orderData.arr.map((item) => item.bingQuID)
        if (!(await this.validateZhiXingBQ(bingQuIDs))) {
          return
        }

        // 8: 调用保存接口
        await this.saveOrders({
          ...orderData,
          type
        })
      } catch (error) {
        console.error('保存医嘱出错:', error)
        this.$message.error('保存医嘱失败：' + error.message || '未知错误')
      } finally {
        this.isSubmitting = false
      }
    },

    // 特殊条件检查
    async validateSpecialConditions() {
      // 检查药品医嘱
      const drugOrders = this.drawerYiZhuList.filter((e) => e.leiBie === '3')
      if (
        String(this.curBrZhuanKeID) === '49' &&
        drugOrders.length > 0 &&
        this.tabActive === 'cq'
      ) {
        await this.$confirm('急诊抢救长期医嘱不能开药品', '信息窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return false
      }

      // 检查病区入院
      if (this.tabActive === 'cq' && !this.inpatientInit?.ezyblbrVo?.bingQuRYRQ) {
        await this.$confirm(
          '病人未病区入院，不能开长期医嘱，可以开临时、化验、特检医嘱！',
          '信息窗口',
          {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          }
        )
        return false
      }

      return true
    },

    // 验证今日临停
    async validateJinRiLingTing(item) {
      if (item.jinRiLT === '1') {
        const planDate = format(new Date(item.kaiShiSJ), 'yyyy-MM-dd')
        const today = format(new Date(), 'yyyy-MM-dd')
        if (planDate !== today) {
          await this.$confirm(
            `【${item.mingCheng}】计划开始时间非当日时不允许勾【今日临停】！`,
            '信息窗口',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            }
          )
          return false
        }
      }
      return true
    },

    // 验证草药用量
    async validateCaoYaoYongLiang(item) {
      if (
        (item.yiZhuLX === 'cy' || item.yiZhuLX === 'zcydy') &&
        (!item.yiCiYL || String(item.yiCiYL) === '0')
      ) {
        await this.$confirm('草药的一次用量不能为空和0', '信息窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return false
      }
      return true
    },

    // 验证单个医嘱出院带药数量
    async validateSingleOrderDrugQuantity(item) {
      if ((!item.shouFeiSL || String(item.shouFeiSL) === '0') && item.yiZhuLX === 'cydy') {
        await this.$confirm('出院带药数量不可为空', '信息窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return false
      }
      return true
    },

    // 验证药品附加信息
    async validateDrugAttributes(item) {
      if (item.FJXX) {
        const { drugAttributesVos } = item.FJXX

        // 不进入出院带药的药品(中成药分餐校验)
        const noFengZhuangItem = drugAttributesVos?.find(
          (item) => item.shuXingDM === '3000' && item.shuXingZhi === '1'
        )
        if (item.leiBie === '3') {
          if (noFengZhuangItem && item.yiZhuLX === 'cydy') {
            await this.$confirm(
              `${item.mingCheng}药品有分装药品，出院带药请使用分装药品`,
              '信息窗口',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              }
            )
            return false
          }
        }

        // 体表面积计算的药品
        const tbmjItem = drugAttributesVos?.find(
          (item) => item.shuXingDM === 'TBMJ' && item.shuXingZhi === '1'
        )
        if (tbmjItem) {
          this.isTBMJYaoPin = true
        }

        // 国家采购判断
        if (item.shiFouZB !== '1' && item.FJXX.guoJiaCG !== '0') {
          if (item.yiZhuLX === 'cydy') {
            if (item.FJXX.canprescribe_cydy === '0') {
              await this.$confirm(
                `国家集采：无法开具${item.mingCheng}，请选择同一通用名的集采药品替代！`,
                '国家集采信息提示',
                {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'info'
                }
              )
              return false
            } else if (item.FJXX.guoJiaCG === '13' && item.shouFeiSL !== '1') {
              await this.$confirm(
                `药品【${item.mingCheng}】的出院带药仅限开一盒！`,
                '国家集采信息提示',
                {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'info'
                }
              )
              return false
            }
          } else {
            if (item.FJXX.canprescribe_zy === '0') {
              await this.$confirm(
                `国家集采：无法开具${item.mingCheng}，请选择同一通用名的集采药品替代！`,
                '国家集采信息提示',
                {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'info'
                }
              )
              return false
            }
          }
        }

        // 是否有必填结束时间
        const jssjItem = drugAttributesVos?.find(
          (item) => item.shuXingDM === 'JSSJ' && item.shuXingZhi === '1'
        )
        if (jssjItem && !item.jieShuSJ) {
          // 必填结束时间检查
          await this.$confirm(`${item.mingCheng}医嘱的必填结束时间`, '信息窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return false
        }

        // 是否自费
        if (
          this.inpatientInit.inPatientVo?.jieSuanLX !== '00' &&
          item.FJXX.kongZhiJBMC.includes('自费')
        ) {
          item.ziFei = '1'
        }
      }
      return true
    },

    // 验证医嘱类型
    async validateOrderType(item) {
      if (this.tabActive === 'cq') {
        if (item.yiZhuLB === '3') {
          await this.$confirm(`${item.mingCheng}是草药，长期医嘱里不能开草药！`, '信息窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return false
        }
      } else {
        if (item.yiZhuLB === '3') {
          if (item.yiZhuLX !== 'zcydy' && item.yiZhuLX !== 'cy') {
            await this.$confirm(
              `${item.mingCheng}是草药，请选择正确的医嘱类型（草药医嘱、草药带药）！`,
              '信息窗口',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              }
            )
            return false
          }
        } else {
          if (item.yiZhuLX !== 'ls' && item.yiZhuLX !== 'cydy') {
            await this.$confirm(
              `${item.mingCheng}不是草药，请选择正确的医嘱类型（临时医嘱、出院带药）！`,
              '信息窗口',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              }
            )
            return false
          }
        }
      }
      return true
    },

    // 验证特殊用法字数限制
    async validateSpecialUsage(item) {
      if (item.teShuYF && item.teShuYF.length > 30) {
        await this.$confirm('特殊用法字数限制30字', '信息窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return false
      }
      return true
    },

    // 验证出院带药选项
    async validateDischargeMedication(item) {
      if (item.yiZhuLX === 'cydy' && !item.jinRiCY) {
        await this.$confirm('必选"预计出院"', '信息窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return false
      }
      return true
    },

    // 验证开始时间和结束时间
    async validateStartTime(item, dateNow) {
      if (item.kaiShiSJ) {
        const startTime = new Date(item.kaiShiSJ).getTime()
        const dateNowTime = new Date(dateNow).getTime()
        const diffHours = Math.abs(startTime - dateNowTime) / (1000 * 60 * 60)

        if (startTime < dateNowTime && diffHours > 6 && !item.teShuYF) {
          await this.$confirm(
            `${item.mingCheng}医嘱的计划开始时间往前超过6小时，请在备注里说明原因! ${item.kaiShiSJ}`,
            '信息窗口',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            }
          )
          return false
        } else if (diffHours > 24 * 365) {
          await this.$confirm(
            `${item.mingCheng}医嘱的计划开始时间有误，请修改！ ${item.kaiShiSJ}`,
            '信息窗口',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            }
          )
          return false
        }
      }
      // 结束时间不能早于开始时间
      if (item.jieShuSJ) {
        const startTime = new Date(item.kaiShiSJ).getTime()
        const endTime = new Date(item.jieShuSJ).getTime()

        if (endTime < startTime) {
          await this.$confirm(`${item.mingCheng}医嘱的结束时间不能早于计划开始时间！`, '信息窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return false
        }
      }
      return true
    },

    // 校验组合医嘱
    async validateGroupOrders(item) {
      // YPDSBZNum 药品滴速备注的数量，超过2个返回true
      let YPDSBZNum = 0
      let YPDSBZRtn = false
      // 当前虚拟组号
      const th1 =
        item.yiZhuLX === 'cy' || item.yiZhuLX === 'zcydy' ? item.xuNiZH.split('-')[0] : item.xuNiZH

      for (const e of this.drawerYiZhuList) {
        if (e.yaoPinDSBZ) {
          YPDSBZNum++
          if (YPDSBZNum >= 2) {
            YPDSBZRtn = true
          }
        }

        const th2 = e.yiZhuLX === 'cy' || e.yiZhuLX === 'zcydy' ? e.xuNiZH.split('-')[0] : e.xuNiZH

        if (th1 === th2 && item.leiBie === '3') {
          // 检查同组用法频率给药时间一致性
          if (
            item.zhiXingFF !== e.zhiXingFF ||
            item.zhiXingPL !== e.zhiXingPL ||
            item.geiYaoSJ !== e.geiYaoSJ
          ) {
            await this.$confirm('用法、频率、用药时间必须与同组药品保持一致', '信息窗口', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            })
            return false
          }

          // 检查草药数量一致性
          if (
            item.shouFeiSL !== e.shouFeiSL &&
            item.shouFeiSL &&
            e.shouFeiSL &&
            (item.yiZhuLX === 'cy' || item.yiZhuLX === 'zcydy')
          ) {
            await this.$confirm('草药的数量必须与同组药品保持一致！', '信息窗口', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            })
            return false
          }

          // 检查预计出院一致性
          if (item.jinRiCY !== e.jinRiCY && (item.yiZhuLX === 'zcydy' || item.yiZhuLX === 'cydy')) {
            await this.$confirm('预计出院必须与同组药品保持一致！', '信息窗口', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            })
            return false
          }

          // 检查出院带药数量
          if ((!item.shouFeiSL || !e.shouFeiSL) && item.yiZhuLX === 'cydy') {
            await this.$confirm('出院带药数量不可为空', '信息窗口', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            })
            return false
          }
        }
      }

      if (YPDSBZRtn) {
        await this.$confirm('同一组药品里只能有一个滴速内容，请删除', '信息窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return false
      }

      return true
    },

    // 校验必填字段
    async validateRequiredFields(item) {
      if (item.leiBie === '1' && !item.zhiXingPL) {
        await this.$confirm(`治疗医嘱${item.mingCheng}必填执行频率`, '信息窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return false
      }

      if (item.leiBie === '3') {
        const missingFields = []
        if (!item.kaiShiSJ) missingFields.push('计划开始时间')
        if (item.yiCiYL === '') missingFields.push('一次用量')
        if (!item.jiLiangDW) missingFields.push('剂量单位')
        if (!item.zhiXingFF) missingFields.push('执行方法')
        if (!item.zhiXingPL) missingFields.push('执行频率')

        if (missingFields.length > 0) {
          await this.$confirm(`${item.mingCheng}必填${missingFields.join('、')}`, '信息窗口', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'info'
          })
          return false
        }

        // 微泵维持检查
        if (item.zhiXingFF === 'iv-vp') {
          const teShuYF = item.teShuYF || ''
          if (!(teShuYF.includes('速度') || teShuYF.includes('流速'))) {
            await this.$confirm(
              '微泵维持必须在医嘱说明中输入中文速度或流速!(例如：流速:3ml/h)',
              '信息窗口',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              }
            )
            return false
          }
        }

        // 自定义用法检查
        if (item.zhiXingFF === '自定义' && !item.teShuYF) {
          await this.$confirm(
            `请填写${item.mingCheng}医嘱的医嘱说明/备注说明！(例如：流速:3ml/h)`,
            '信息窗口',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'info'
            }
          )
          return false
        }
      }

      return true
    },

    // 构建保存数据
    buildSaveData(item) {
      // 处理虚拟组号
      let xuNiZH = item.xuNiZH
      if (item.yiZhuLX === 'cy' || item.yiZhuLX === 'zcydy') {
        xuNiZH = item.xuNiZH.split('-')[0]
      }
      // 处理项目ID
      let xiangMuID = item.xiangMuID || item.val
      if (item.leiBie === '3') {
        xiangMuID = item.xiangMuID || item.yaoPinID
      }
      // 如果是嘱托
      if (item.leiBie === '4') {
        xiangMuID = 't6712'
      }

      return {
        crrt: item.crrt || '',
        crrtYzVos: item.crrtYzVos || [],
        gcp: item.gcp || '',
        gcpywbh: item.gcpywbh || '',
        yiZhuID: item.yiZhuID || 0,
        leiBie: item.leiBie || '',
        yiZhuLX: item.yiZhuLX,
        guanLianYZID: item.guanLianYZID || 0,
        guoJiaCG: item.guoJiaCG || '',
        jingPeiYP: item.jingPeiYP || '', // 静脉营养药品（药品附加属性值）
        xuNiZH: xuNiZH,
        bingLiID: this.bingLiID,
        vteYzVo: item.vteYzVo || {},
        yaoPinZdVos: item.yaoPinZdVos || [],
        zhuYuanID: this.inpatientInit.inPatientVo.zhuYuanID,
        zhiLiaoyzmxVos: item.zhiLiaoyzmxVos || [],
        zhuanKeID: this.curBrZhuanKeID,
        bingQuID: item.bingQuID || this.inpatientInit?.ezyblbrVo?.bingQuID,
        kangJunYaoYfVo: item.kangJunYaoYfVo || {},
        ziFei: item.ziFei || '0',
        xiangMuID: xiangMuID,
        ZuHao: item.zuHao || '',
        yaoFangDM: item.yaoPinData?.yaoFangDM || item.yaoFangDM || '',
        mingCheng: item.mingCheng,
        danJia: item.yaoPinData?.shouJia || item.danJia || '',
        guiGe: item.guiGe || '',
        jiXing: item.yaoPinData?.jiXing || item.jiXing || '',
        yiCiYL: item.yiCiYL,
        jiLiangDW: item.jiLiangDW,
        zhiXingFF: item.zhiXingFF || '',
        zhiXingPL: item.zhiXingPL || '',
        geiYaoSJ: item.geiYaoSJ || '',
        teShuYF: item.teShuYF || '',
        kaiShiSJ: item.kaiShiSJ || '',
        jieShuSJ: item.jieShuSJ || '',
        chiXuTS: item.chiXuTS || '',
        yiZhuLB: item.yiZhuLB || item.yaoPinData?.yiZhuLB || '',
        shouFeiSL:
          item.yiZhuLX === 'cydy' ||
          ((item.yiZhuLX === 'ls' || item.yiZhuLX === 'cq') &&
            (item.leiBie === '1' || item.leiBie === '4'))
            ? item.shouFeiSL || '1'
            : '',
        shouFeiCS:
          item.yiZhuLX === 'cy' || item.yiZhuLX === 'zcydy'
            ? item.shouFeiSL || item.shouFeiCS || 7
            : '',
        zhuangTaiBZ: item.zhuangTaiBZ || item.yaoPinData?.zhuangTaiBZ,
        luRuSJ: item.luRuSJ || '',
        yiShengID: this.yiShengID,
        tingZhiSJ: item.tingZhiSJ || '',
        tingZhiYSID: item.tingZhiYSID || '',
        cheXiaoZT: item.cheXiaoZT || '',
        cheXiaoSJ: item.cheXiaoSJ || '',
        cheXiaoRYID: item.cheXiaoRYID || '',
        xuShenPi: item.xuShenPi || '',
        shenQingSL: item.shenQingSL || '',
        tongZhiDanID: item.tongZhiDanID || '',
        kangJunYP: item.kangJunYP || '',
        zuHaoTXT: item.zuHaoTXT || '',
        bianMaLX: item.bianMaLX || '',
        xiangMuBM: item.xiangMuBM || '',
        yongYaoTS: item.yongYaoTS || '',
        kaiDanYSZKID: item.kaiDanYSZKID || '',
        mianPiShi: item.mianPiShi || '',
        shiFouBL: item.shiFouBL || '',
        danWei: item.danWei || '',
        shenQingDanSJID: item.shenQingDanSJID || 0,
        yaoPinLB: item.yaoPinLB || '',
        shiFouZB: item.shiFouZB || '',
        teShuKJYWHZDID: item.teShuKJYWHZDID || 0,
        sanLianKJYWHZDID: item.sanLianKJYWHZDID || 0,
        jinRiCY: item.jinRiCY || '',
        zanShiBQ: item.zanShiBQ || '',
        jiBingDM: item.jiBingDM || '',
        jiBingMC: item.jiBingMC || '',
        shenQingYY: item.shenQingYY || '',
        yongYaoSQ: item.yongYaoSQ || '',
        yaoPinDS: item.yaoPinDS || '',
        changWaiYYHZDID: item.changWaiYYHZDID || 0,
        zhiXingPL2: item.zhiXingPL2 || '',
        zhongChengYaoZD: item.zhongChengYaoZD || '',
        zhongChengYaoZZ: item.zhongChengYaoZZ || '',
        yaoPinDSBZ: item.yaoPinDSBZ || '',
        waiGouYPSQJLID: item.waiGouYPSQJLID || 0,
        laiYuan: item.laiYuan || '',
        maZuiYT: item.maZuiYT || '',
        xianDingFW: item.xianDingFW || '',
        fenLeiMa: item.fenLeiMa || '',
        sheBaoDM: item.FJXX?.sheBaoDM || item.sheBaoDM || '',
        sheBaoDM2: item.FJXX?.sheBaoDM2 || item.sheBaoDM2 || '',
        laiYuanLB: item.laiYuanLB || '',
        jiGouDM: item.jiGouDM || '',
        jinRiLT: item.jinRiLT || '',
        tingZhiLX: item.tingZhiLX || '',
        jianFa: item.jianFa || '',
        liShiYYTS: item.FJXX?.liShiYYTS || item.liShiYYTS || ''
      }
    },

    // 构建规则引擎数据
    async buildRuleEngineData(arr, dateNow) {
      console.log(this.inpatientInit)
      try {
        const arr_sskg = []
        const alertQueue = []
        let linShiSJ = {
          Charges: [], //治疗
          Drugs: [] //药品
        }
        arr.forEach((item) => {
          if (item.leiBie === '2') {
            linShiSJ.Charges.push({
              xiangMuMC: item.mingCheng,
              shengJiBM: item.sheBaoDM2, //item.GJM,
              guoJiaBM: item.sheBaoDM,
              shuLiang: parseInt(shouFeiSL)
            })
          } else if (item.leiBie === '3') {
            const startTime = new Date(item.kaiShiSJ).getTime()
            const dateNowTime = new Date(item.jieShuSJ).getTime()
            let diffDays = Math.abs(dateNowTime - startTime) / (1000 * 60 * 60 * 24)
            if (diffDays < 1) diffDays = 1
            const num = diffDays + parseInt(item.liShiYYTS || '')
            linShiSJ.Drugs.push({
              yiShiID: this.yiShengID,
              zhuanKeID: parseInt(this.curYSZhuanKeID),
              fenLeiMa: item.fenLeiMa,
              yaoPinID: parseInt(item.xiangMuID),
              mingCheng: item.mingCheng,
              jiXing: item.jiXing,
              yiCiYL: parseFloat(item.yiCiYL),
              jiLiangDW: item.jiLiangDW,
              pinLv: item.zhiXingPL,
              fangFa: item.zhiXingFF,
              geiYaoSJ: item.geiYaoSJ, //item.GYSJ
              teShuYF: item.teShuYF,
              yiZhuSJ: item.luRuSJ || dateNow,
              yiShiYongTS: diffDays + parseInt(item.liShiYYTS || ''), //diffDays + parseInt(item.liShiYYTS || "")
              BMI: this.inpatientInit.bmi,
              guoJiaBM: item.sheBaoDM
            })
          }
          arr_sskg.push({
            xiangMuID: item.xiangMuID,
            leiBie: 1,
            guoJiaBM: item.sheBaoDM
          })
        })
        this.visible = true
        this.isOptions = {
          // "biaoShiHao": this.bingLiID, //诊疗活动ID/病例ID
          // "bingAnHao": this.initInfo.bingAnHao, //病案号
          biaoShiHao: 3178318, //标识号
          bingAnHao: '**********', //病案号
          changJingDM: 'cwsf_zyyz', //场景代码
          yingYongDM: '020', //
          yongHuID: this.yiShengID, //
          //临时数据
          linShiSJ: linShiSJ,
          linShiZD: {},
          shiFouMZ: false, //是否门诊
          isItemOfRuleEngine: true
        }
        return new Promise((resolve) => {
          const handler = (result, obj) => {
            //result===1无需操作，2改为自费，3阻断或取消
            if (result === 1) {
              resolve(false)
            } else if (result === 2) {
              // 用户修改为自费
              if (obj) {
                const idx = obj.chaRuSX - 1
                const list = linShiSJ[obj.shuJuLeiDM][idx]
                if (list) {
                  arr.forEach((ev) => {
                    if (ev.sheBaoDM === list.guoJiaBM) {
                      ev.ziFei = 1
                    }
                  })
                }
              }
              resolve({ type: true, arr: arr })
            } else if (result === 3) {
              // 阻断或取消
              resolve(true)
            }
          }
          this.$once('PayingResult', handler)
        })
      } catch {
        return false
      }
    },

    // 校验执行病区
    async validateZhiXingBQ(bingQuIDs) {
      try {
        const res = await checkZhiXingBQ({
          zhiXingBQs: bingQuIDs,
          bingLiID: this.bingLiID
        })
        if (res.hasError === 0 && res.data === '1') {
          try {
            await this.$confirm(res.errorMessage, '信息窗口', {
              type: 'info'
            })
          } catch (error) {
            return false
          }
        }
        return true
      } catch (error) {
        this.$message.error(error?.message || '校验执行病区出错')
        return false
      }
    },

    // 调用保存医嘱接口
    async saveOrders(params) {
      const { arr, ganRanZD, teShuYWBAs, type } = params
      try {
        const res = await saveYiZhu({
          bingLiID: this.bingLiID,
          yiZhuLB: this.tabActive,
          ylYzallExtendVos: arr, // 医嘱列表
          shanChuYzIds: this.shanChuYZIDList, // 删除医嘱ID列表
          ganRanZD, // 感染诊断
          teShuYWBAs // 特殊药物备案
        })

        if (res.hasError === 0) {
          if (type === 'save') {
            this.$message.success('保存医嘱成功')
          }
          await this.getUncommitYZ()
          this.shanChuYZIDList = []
          if (type === 'submit') {
            await this.beforeSubmit()
            await this.getBingRenYZ()
            await this.getUncommitYZ()
          }
        }
      } catch (error) {
        throw error
      }
    },

    // 提交前判断
    async beforeSubmit() {
      try {
        // 开始提交流程
        await this.startSubmitProcess()
      } catch (error) {
        console.error('提交医嘱出错:', error)
        this.$message.error(error?.message || '提交医嘱出错')
      } finally {
        this.isSubmitting = false
      }
    },

    // 医嘱提交流程控制
    async startSubmitProcess() {
      // 定义提交步骤
      const steps = [
        this.checkHeightWeight, // 1.检查身高体重
        this.checkChineseHerbal, // 2.检查草药代煎
        this.checkDepartmentMatch, // 3.检查专科匹配
        this.checkPreOperativeOrders, // 4.检查术前医嘱
        this.checkDischargeOrders, // 5.检查出院带药
        this.checkHerbalDelivery, // 6.检查草药快递
        this.checkRationalMedication, // 7.检查合理用药
        this.finalSubmit // 最终提交
      ]

      // 按顺序执行步骤
      for (const step of steps) {
        const shouldContinue = await step()
        if (!shouldContinue) {
          return false // 中断流程
        }
      }

      return true // 所有步骤完成
    },

    // 1. 检查身高体重
    async checkHeightWeight() {
      // 如果不是需要体表面积的药品，直接跳过
      if (!this.isTBMJYaoPin) return true

      try {
        const res = await getLastSGTZ({ bingLiID: this.bingLiID })
        if (res.hasError === 0 && res.data) {
          const data = res.data
          this.lastSGTZ = {
            shenGao: data?.shenGao?.zhi,
            tiZhong: data?.tiZhong?.zhi,
            caoZuoZhe: data?.shenGao?.caoZuoZhe,
            ceLiangSJ: data?.shenGao?.ceLiangSJ
          }

          // 根据年龄计算体表面积
          if (this.inpatientInit.ageNumber > 16) {
            this.TBMJ = (
              0.0061 * Number(this.lastSGTZ.shenGao) +
              0.0128 * Number(this.lastSGTZ.tiZhong) -
              0.1259
            ).toFixed(4)
            this.TBMJText = '0.0061*身高(cm)+0.0128*体重(kg)-0.1259'
          } else if (this.inpatientInit.ageNumber <= 30) {
            this.TBMJ = (0.035 * Number(this.lastSGTZ.tiZhong) + 0.1).toFixed(4)
            this.TBMJText = '体重(kg)*0.035+0.1'
          } else {
            this.TBMJ = (1.05 + (Number(this.lastSGTZ.tiZhong) - 30) * 0.02).toFixed(4)
            this.TBMJText = '1.05+(体重(kg)-30)*0.02'
          }

          // 显示体表面积弹窗
          this.tbmjDialog = true

          // 等待体表面积弹窗确认后，继续执行提交
          return new Promise((resolve) => {
            this.$once('tbmjDialogConfirm', (confirmed) => {
              if (confirmed) {
                resolve(true) // 继续流程
              } else {
                resolve(false) // 中断流程
              }
            })
          })
        }
      } catch (error) {
        this.$message.error(error?.message || '获取身高体重数据出错')
        return false
      }

      return true
    },

    // 体表面积计算
    handleTBMJInput() {
      if (!this.lastSGTZ.shenGao || !this.lastSGTZ.tiZhong) return

      if (this.inpatientInit.ageNumber > 16) {
        this.TBMJ = (
          0.0061 * Number(this.lastSGTZ.shenGao) +
          0.0128 * Number(this.lastSGTZ.tiZhong) -
          0.1259
        ).toFixed(4)
      } else if (this.inpatientInit.ageNumber <= 30) {
        this.TBMJ = (0.035 * Number(this.lastSGTZ.tiZhong) + 0.1).toFixed(4)
      } else {
        this.TBMJ = (1.05 + (Number(this.lastSGTZ.tiZhong) - 30) * 0.02).toFixed(4)
      }
    },

    // 体表面积弹窗确认
    async handleTBMJDialogConfirm() {
      try {
        if (!this.lastSGTZ.shenGao || !this.lastSGTZ.tiZhong) {
          throw new Error('身高体重不能为空')
        }

        if (
          Number(this.lastSGTZ.shenGao) > 300 ||
          Number(this.lastSGTZ.shenGao) < 30 ||
          Number(this.lastSGTZ.tiZhong) > 300 ||
          Number(this.lastSGTZ.tiZhong) < 10
        ) {
          await this.$confirm('身高/体重异常是否确定保存', '提示', {
            type: 'warning'
          })
        }

        // 保存最新身高体重
        const res = await saveLastSGTZ({
          bingLiID: this.bingLiID,
          zhuYuanID: this.inpatientInit.inPatientVo.zhuYuanID,
          bingQuID: this.inpatientInit.inPatientVo.bingQuID,
          bingRenXM: this.inpatientInit.inPatientVo.bingRenXM,
          ceLiangSJ: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          tiZhong: this.lastSGTZ.tiZhong,
          shenGao: this.lastSGTZ.shenGao
        })

        if (res.hasError === 0) {
          this.tbmjDialog = false
          // 发送确认事件,继续提交流程
          this.$emit('tbmjDialogConfirm', true)
        }
      } catch (error) {
        this.$message.error(error?.message || '保存身高体重出错')
      }
    },

    // 体表面积弹窗取消
    handleTBMJDialogCancel() {
      this.tbmjDialog = false
      this.$emit('tbmjDialogConfirm', false)
    },

    // 2. 检查草药代煎
    async checkChineseHerbal() {
      // 筛选草药医嘱
      const caoyaoList = this.drawerYiZhuList.filter((item) => item.yiZhuLB === '3')

      if (caoyaoList.length > 0) {
        // 默认为非草药
        this.cyDaiJian = '9'

        for (const item of caoyaoList) {
          // Mjkl特殊剂型不需要代煎
          if (item.jiXing === 'Mjkl') {
            this.cyDaiJian = '0'
            break
          }
          // 中医科的中成药出院带药不需要代煎
          if (String(this.curYSZhuanKeID) === '33' && item.yiZhuLX === 'zcydy') {
            this.cyDaiJian = '0'
            break
          }
        }

        // 如果不是特殊情况且尚未设置代煎标志
        if (this.cyDaiJian === '9') {
          try {
            // 确认是否代煎
            await this.$confirm('是否由我院代煎?', '提示信息', {
              confirmButtonText: '代煎',
              cancelButtonText: '不代煎',
              type: 'warning'
            })
            // 用户选择代煎
            this.cyDaiJian = '1'
          } catch (error) {
            // 用户选择自煎
            this.cyDaiJian = '0'
          }
        }
      } else {
        // 非草药设置为9
        this.cyDaiJian = '9'
      }

      return true // 继续流程
    },

    // 3. 检查专科是否一致
    async checkDepartmentMatch() {
      // 判断医生专科和病人专科是否一致
      if (String(this.curYSZhuanKeID) !== String(this.curBrZhuanKeID)) {
        try {
          await this.$confirm('医生专科和病人专科不一致,是否继续提交?', '提示信息', {
            type: 'warning'
          })
        } catch (error) {
          return false // 用户取消，中断流程
        }
      }
      return true // 继续流程
    },
    // 4. 检查术前医嘱
    async checkPreOperativeOrders() {
      try {
        // 校验术前医嘱自动开皮试医嘱
        const shuQianRes = await checkShuQianYz({
          bingLiID: this.bingLiID,
          yiZhuIDs: this.drawerYiZhuList.map((item) => item.yiZhuID)
        })

        if (shuQianRes.hasError === 0 && shuQianRes.data === '1') {
          try {
            await this.$confirm(
              '需皮试医嘱开至手术室是否自动生成一条皮试医嘱到本病区？',
              '信息窗口',
              {
                type: 'warning',
                confirmButtonText: '是',
                cancelButtonText: '否'
              }
            )
            this.piShiYz = '1'
          } catch (error) {
            this.piShiYz = '0'
          }
        }
      } catch (error) {
        this.$message.error(error?.message || '校验术前医嘱出错')
        return false
      }
      return true // 继续流程
    },

    // 5. 检查出院带药
    async checkDischargeOrders() {
      // 筛选出院带药医嘱
      const cydyList = this.drawerYiZhuList.filter((item) => item.yiZhuLX === 'cydy')

      if (cydyList.length > 0) {
        try {
          const chuYuanRes = await checkJinRiCY({
            bingLiID: this.bingLiID,
            yiZhuIDs: this.drawerYiZhuList.map((item) => item.yiZhuID)
          })

          if (chuYuanRes.hasError === 0) {
            try {
              await this.$confirm(chuYuanRes.data, '提示窗口', {
                type: 'warning'
              })
            } catch (error) {
              return false // 用户取消，中断流程
            }
          }
        } catch (error) {
          this.$message.error(error?.message || '校验出院带药出错')
          return false
        }
      }
      return true // 继续流程
    },
    // 6. 检查草药快递
    async checkHerbalDelivery() {
      // 筛选需要快递的草药医嘱
      const cyList = this.drawerYiZhuList.filter(
        (item) => item.jinRiCY === '3' || item.yiZhuLX === 'zcydy'
      )

      if (cyList.length > 0) {
        try {
          const kuaiDiRes = await getZyKuaiDiDz({
            bingLiID: this.bingLiID
          })

          if (kuaiDiRes.hasError === 0) {
            // 设置快递地址
            if (kuaiDiRes.data) {
              this.zykdAddress = {
                shouHuoRenXM: kuaiDiRes.data.shouHuoRenXM || '',
                lianXiDH: kuaiDiRes.data.lianXiDH || '',
                shengFen: kuaiDiRes.data.shengFen || '330000',
                chengShi: kuaiDiRes.data.chengShi || '330300',
                qu: kuaiDiRes.data.qu || '',
                lianXiDZ: kuaiDiRes.data.lianXiDZ || '',
                xiuGaiSJ: kuaiDiRes.data.xiuGaiSJ || '',
                xiuGaiYHID: kuaiDiRes.data.xiuGaiYHID || '',
                beiZhu: kuaiDiRes.data.beiZhu || ''
              }
            }

            // 初始化地区选项
            await this.getProvinceOptions()

            // 如果已有省市区数据,初始化下拉框
            if (this.zykdAddress.shengFen) {
              await this.getCityOptions(this.zykdAddress.shengFen)
              if (this.zykdAddress.chengShi) {
                await this.getCountyOptions(this.zykdAddress.chengShi)
              }
            }

            // 显示快递弹窗
            this.zykdDialog = true

            // 等待快递弹窗处理完成
            return new Promise((resolve) => {
              this.$once('zykdDialogConfirm', (saved) => {
                if (saved) {
                  resolve(true) // 继续流程
                } else {
                  resolve(false) // 中断流程
                }
              })
            })
          }
        } catch (error) {
          this.$message.error(error?.message || '获取草药快递地址出错')
          return false
        }
      }
      return true // 继续流程
    },
    // 处理省份变化
    handleProvinceChange(value) {
      // 重置市和区
      this.zykdAddress.chengShi = ''
      this.zykdAddress.qu = ''
      this.cityOptions = []
      this.countyOptions = []

      // 获取选中省份的城市数据
      this.getCityOptions(value)
    },
    // 处理城市变化
    handleCityChange(value) {
      // 重置区
      this.zykdAddress.qu = ''
      this.countyOptions = []

      // 获取选中城市的区县数据
      this.getCountyOptions(value)
    },
    // 获取省份选项
    async getProvinceOptions() {
      try {
        const res = await getProvinceOptions()
        if (res.hasError === 0 && res.data) {
          this.provinceOptions = res.data.map((item) => ({
            label: item.jiGuanMC,
            value: item.jiGuanDM
          }))
        }
      } catch (error) {
        console.error('获取省份数据失败:', error)
      }
    },
    // 获取城市选项
    async getCityOptions(provinceCode) {
      try {
        const res = await getCityOptions({
          shengFenDM: provinceCode
        })
        if (res.hasError === 0 && res.data) {
          this.cityOptions = res.data.map((item) => ({
            label: item.jiGuanMC,
            value: item.jiGuanDM
          }))
        }
      } catch (error) {
        console.error('获取城市数据失败:', error)
      }
    },
    // 获取区县选项
    async getCountyOptions(cityCode) {
      try {
        const res = await getCountyOptions({
          chengShiDM: cityCode
        })
        if (res.hasError === 0 && res.data) {
          this.countyOptions = res.data.map((item) => ({
            label: item.jiGuanMC,
            value: item.jiGuanDM
          }))
        }
      } catch (error) {
        console.error('获取区县数据失败:', error)
      }
    },
    // 保存快递地址
    async saveZykdAddress() {
      // 验证表单数据
      const { shouHuoRenXM, lianXiDH, shengFen, chengShi, qu, lianXiDZ } = this.zykdAddress

      // 验证收货人
      if (!shouHuoRenXM) {
        this.$message.error('收货人不能为空')
        return
      }

      // 验证手机号
      if (!lianXiDH) {
        this.$message.error('手机号码不能为空')
        return
      }

      // 验证手机号格式
      const phoneReg = /^1[3-9]\d{9}$/
      if (!phoneReg.test(lianXiDH)) {
        this.$message.error('请输入正确的手机号码')
        return
      }

      // 验证省份
      if (!shengFen) {
        this.$message.error('请选择省份')
        return
      }

      // 验证城市
      if (!chengShi) {
        this.$message.error('请选择城市')
        return
      }

      // 验证区/县
      if (!qu) {
        this.$message.error('请选择区/县')
        return
      }

      // 验证详细地址
      if (!lianXiDZ) {
        this.$message.error('详细地址不能为空')
        return
      }

      try {
        const params = {
          bingLiID: this.bingLiID,
          shouHuoRenXM: this.zykdAddress.shouHuoRenXM,
          lianXiDH: this.zykdAddress.lianXiDH,
          shengFen: this.zykdAddress.shengFen,
          chengShi: this.zykdAddress.chengShi,
          qu: this.zykdAddress.qu,
          lianXiDZ: this.zykdAddress.lianXiDZ,
          xiuGaiSJ: this.zykdAddress.xiuGaiSJ,
          xiuGaiYHID: this.zykdAddress.xiuGaiYHID,
          beiZhu: this.zykdAddress.beiZhu
        }

        const res = await saveZyKuaiDiDz(params)

        if (res.hasError === 0) {
          this.$message.success('快递地址保存成功')
          this.zykdDialog = false
          this.$emit('zykdDialogConfirm', true)
        } else {
          throw new Error(res.message || '保存快递地址失败')
        }
      } catch (error) {
        if (error.message) {
          this.$message.error(error.message)
        }
      }
    },
    // 7. 检查合理用药
    async checkRationalMedication() {
      try {
        // TODO: 调用合理用药判断接口
        // 未实现,暂时直接返回继续
      } catch (error) {
        this.$message.error(error?.message || '合理用药校验出错')
        return false
      }
      return true // 继续流程
    },
    // 8. 最终提交医嘱
    async finalSubmit() {
      try {
        // 调用提交医嘱接口
        const submitRes = await submitYZ({
          bingLiID: this.bingLiID,
          yiZhuLB: this.tabActive,
          cyDaiJian: this.cyDaiJian,
          piShiYz: this.piShiYz,
          yzIds: this.drawerYiZhuList.map((item) => item.yiZhuID)
        })

        if (submitRes.hasError === 0) {
          this.$message.success('提交医嘱成功')
          await this.getBingRenYZ()
          await this.getUncommitYZ()
          return true
        } else {
          throw new Error(submitRes.message || '提交医嘱失败')
        }
      } catch (error) {
        this.$message.error(error?.message || '提交医嘱失败')
        return false
      }
    },
    // 历史用药下拉框
    handleHistoryDrug(command) {
      this.historyDrugList = []
      switch (command) {
        case '1': // 历史用药
          this.historyDrugTitle = '历史用药'
          this.historyDrugDialog = true
          if (this.inpatientInit?.inPatientVo?.bingQuRuYuanSJ) {
            this.historyDrugDateRange[0] =
              this.inpatientInit.inPatientVo.bingQuRuYuanSJ.split(' ')[0]
          } else {
            this.historyDrugDateRange[0] = format(new Date(), 'yyyy-MM-dd')
          }
          this.historyDrugDateRange[1] = format(new Date(), 'yyyy-MM-dd')
          getLiShiYyMb({
            bingLiID: this.bingLiID,
            oldBLID: this.bingLiID,
            moBanLX: this.historyDrugType,
            kaiShiSJ: this.historyDrugDateRange[0],
            jieShuSJ: this.historyDrugDateRange[1]
          }).then((res) => {
            if (res.hasError === 0) {
              this.historyDrugList = res.data
            }
          })
          break
        case '2': // 在用口服药
          this.historyDrugTitle = '在用口服药'
          this.historyDrugDialog = true
          getYaoPinMbByLb({
            bingLiID: this.bingLiID,
            muBanLB: '4',
            yiZhuLX: this.tabActive
          }).then((res) => {
            if (res.hasError === 0) {
              this.historyDrugList = res.data
            }
          })
          break
        case '3': // 在用针剂
          this.historyDrugTitle = '在用针剂'
          this.historyDrugDialog = true
          getYaoPinMbByLb({
            bingLiID: this.bingLiID,
            muBanLB: 'zyzj',
            yiZhuLX: this.tabActive
          }).then((res) => {
            if (res.hasError === 0) {
              this.historyDrugList = res.data
            }
          })
          break
        case '4': // 历次住院用药
          this.historyDrugTitle = '历次住院用药'
          this.previousHospitalDialog = true
          this.previousHospitalList = []
          getBinRenXXNoQuanXianByParam({
            param: this.inpatientInit.inPatientVo?.bingAnHao, // 病案号
            type: '1'
          }).then((res) => {
            if (res.hasError === 0 && res.data) {
              // 取第2-4条记录（索引1-3）
              const filteredData = res.data.filter((item, index) => index > 0 && index <= 3)
              this.previousHospitalList = filteredData
            }
          })
          break
      }
    },
    handleHistoryDrugTypeChange(val) {
      if (this.historyDrugDateRange && this.historyDrugDateRange.length === 2) {
        const oldBLID =
          this.historyDrugTitle === '历次住院用药'
            ? this.selectedHospitalRecord?.bingLiID
            : this.bingLiID

        getLiShiYyMb({
          bingLiID: this.bingLiID,
          oldBLID: oldBLID,
          moBanLX: val,
          kaiShiSJ: this.historyDrugDateRange[0],
          jieShuSJ: this.historyDrugDateRange[1]
        }).then((res) => {
          if (res.hasError === 0) {
            this.historyDrugList = res.data
          }
        })
      }
    },
    handleHistoryDrugDateRangeChange(val) {
      if (val && val.length) {
        // 适用于普通历史用药和历次住院用药
        const oldBLID =
          this.historyDrugTitle === '历次住院用药'
            ? this.selectedHospitalRecord?.bingLiID
            : this.bingLiID

        getLiShiYyMb({
          bingLiID: this.bingLiID,
          oldBLID: oldBLID,
          moBanLX: this.historyDrugType,
          kaiShiSJ: val[0],
          jieShuSJ: val[1]
        }).then((res) => {
          if (res.hasError === 0) {
            this.historyDrugList = res.data
          }
        })
      }
    },
    spanMethod({ row, column, rowIndex }) {
      // 动态获取当前表格数据源
      let currentTableData = []

      // 根据当前上下文判断使用哪个数据源
      if (this.historyDrugDialog && this.historyDrugList.length > 0) {
        currentTableData = this.historyDrugList
      } else if (this.virtualDrugDialog && this.virtualDrugList.length > 0) {
        currentTableData = this.virtualDrugList
      } else if (this.chinesePatentDrugDialog && this.chinesePatentDrugList.length > 0) {
        currentTableData = this.chinesePatentDrugList
      } else if (this.saveMBDialog && this.saveMBList.length > 0) {
        currentTableData = this.saveMBList
      }

      // 使用通用的表格行合并方法
      if (currentTableData.length > 0) {
        const spanMethod = createGenericSpanMethod(currentTableData)
        return spanMethod({ row, column, rowIndex })
      }

      // 如果没有找到对应的数据源，返回默认值
      return [1, 1]
    },

    // 为不同表格创建专用的 spanMethod 方法
    historyDrugSpanMethod({ row, column, rowIndex }) {
      const spanMethod = createGenericSpanMethod(this.historyDrugList)
      return spanMethod({ row, column, rowIndex })
    },

    virtualDrugSpanMethod({ row, column, rowIndex }) {
      const spanMethod = createGenericSpanMethod(this.virtualDrugList)
      return spanMethod({ row, column, rowIndex })
    },

    chinesePatentDrugSpanMethod({ row, column, rowIndex }) {
      const spanMethod = createGenericSpanMethod(this.chinesePatentDrugList)
      return spanMethod({ row, column, rowIndex })
    },

    personalMuBanMingXiSpanMethod({ row, column, rowIndex }) {
      const spanMethod = createGenericSpanMethod(this.personalMuBanMingXi)
      return spanMethod({ row, column, rowIndex })
    },

    saveMBSpanMethod({ row, column, rowIndex }) {
      const spanMethod = createGenericSpanMethod(this.saveMBList)
      return spanMethod({ row, column, rowIndex })
    },

    // 判断是否应该显示日期（用于隐藏重复的相邻日期）
    shouldShowDate(rowIndex, dateField = 'kaiShiSJ_rq') {
      return shouldShowRepeatedField(this.yiZhuList, rowIndex, dateField)
    },

    // 获取要显示的日期内容
    getDisplayDate(row, rowIndex, dateField = 'kaiShiSJ_rq') {
      return getDisplayFieldValue(this.yiZhuList, row, rowIndex, dateField)
    },
    // 历史用药选择
    async handleHistoryDrugSelect(row) {
      await this.handleDrugSelect(row, this.historyDrugList)
      this.historyDrugDialog = false
    },
    // 选择历次住院记录
    handleSelectHospitalRecord(record) {
      this.selectedHospitalRecord = record
      this.previousHospitalDialog = false
      this.historyDrugDialog = true
      this.historyDrugTitle = '历次住院用药'
      this.historyDrugType = 'lscq'
      this.historyDrugList = []

      // 设置日期范围
      const startDate = record.bingQuRYRQ ? record.bingQuRYRQ.split(' ')[0] : ''
      const endDate = record.bingQuCYRQ ? record.bingQuCYRQ.split(' ')[0] : ''
      this.historyDrugDateRange = [startDate, endDate]

      // 获取历史用药数据
      this.loadPreviousHospitalDrug()
    },
    // 加载历次住院用药数据
    loadPreviousHospitalDrug() {
      if (
        !this.selectedHospitalRecord ||
        !this.historyDrugDateRange ||
        this.historyDrugDateRange.length !== 2
      )
        return

      getLiShiYyMb({
        bingLiID: this.bingLiID,
        oldBLID: this.selectedHospitalRecord.bingLiID,
        moBanLX: this.historyDrugType,
        kaiShiSJ: this.historyDrugDateRange[0],
        jieShuSJ: this.historyDrugDateRange[1]
      }).then((res) => {
        if (res.hasError === 0) {
          this.historyDrugList = res.data
        }
      })
    },
    // 返回历次住院记录列表
    handleBackToPreviousHospitalList() {
      this.historyDrugDialog = false
      this.previousHospitalDialog = true
      this.selectedHospitalRecord = null
      this.historyDrugList = []
    },
    // 模板选择通用
    async handleDrugSelect(row, list) {
      try {
        // 获取上一条医嘱
        const lastOrderIndex = this.drawerYiZhuList.length - 1
        const lastOrder = lastOrderIndex >= 0 ? this.drawerYiZhuList[lastOrderIndex] : null
        // 获取开始时间
        let startTime
        if (this.copyLastTime && lastOrder) {
          startTime = lastOrder.kaiShiSJ
        } else {
          startTime = await this.getCurrentTime()
        }

        // 获取病区ID
        let bingQuID
        if (this.copyLastBingQu && lastOrder) {
          bingQuID = lastOrder.bingQuID
        } else {
          bingQuID = this.inpatientInit?.ezyblbrVo?.bingQuID?.toString() || ''
        }

        let baseXuNiZH = '1' // 默认第一个组号
        if (lastOrder) {
          if (!lastOrder.mingCheng) {
            // 如果最后一行是空的，则复用它的组号
            baseXuNiZH = lastOrder.xuNiZH.split('-')[0]
          } else {
            // 否则，计算下一个组号
            const maxZuHao = Math.max(
              0,
              ...this.drawerYiZhuList.map((item) => {
                const num = item.xuNiZH?.split('-')[0] || '0'
                return parseInt(num) || 0
              })
            )
            baseXuNiZH = (maxZuHao + 1).toString()
          }
        }
        // 获取相同组号的所有医嘱
        const groupOrders = list.filter((item) => item.zuHao && item.zuHao === row.zuHao)

        // 如果是组合医嘱
        if (groupOrders.length > 1) {
          // 处理多条医嘱
          for (const [idx, order] of groupOrders.entries()) {
            // 将基础组号和索引传递下去，用于处理草药的特殊组号
            await this.processDrug(order, startTime, bingQuID, baseXuNiZH, idx)
          }
        } else {
          // 单个医嘱处理
          await this.processDrug(row, startTime, bingQuID, baseXuNiZH, 0) // 单个项目索引为0
        }

        // 重新排序组号
        this.reorderZuHao()
      } catch (error) {
        console.error('选择药品出错:', error)
        this.$message.error('选择药品出错')
      }
    },

    // 处理单条
    async processDrug(order, startTime, bingQuID, baseXuNiZH, itemIndex) {
      const yaoPinData = {
        baoZhuangLiang: order.baoZhuangLiang,
        yaoPinID: order.yaoPinID,
        fenZhuangQianYPID: order.fenZhuangQYPID,
        yaoPinLB: order.yaoPinLB,
        yaoFangDM: order.yaoFangDM,
        jiLiang: order.jiLiang,
        yiZhuLB: order.yiZhuLB // 1-西药 2-中成药 3-草药
      }

      // 创建新医嘱对象
      const newOrder = {
        mingCheng: order.mingCheng,
        leiBie: '3', // 药品模板leiBie写死3
        yiZhuLX: this.tabActive,
        yiCiYL: order.yiCiYL,
        jiLiangDW: order.jiLiangDW,
        zhiXingFF: order.fangFa,
        zhiXingPL: order.pinLv,
        geiYaoSJ: order.geiYaoSJ,
        shouFeiSL: order.shuLiang || '1',
        kaiShiSJ: startTime,
        bingQuID: bingQuID,
        xuNiZH: baseXuNiZH,
        yaoPinData: yaoPinData
      }

      // 长期医嘱不能导入草药模板
      if (this.tabActive === 'cq' && yaoPinData.yiZhuLB === '3') {
        this.$message.error(`${newOrder.mingCheng}是草药，长期医嘱不能导入草药模板！`)
        return
      }

      // 根据药品类型(yiZhuLB)调整医嘱类型(yiZhuLX) 和 虚拟组号(xuNiZH)
      if (yaoPinData.yiZhuLB === '3') {
        // 如果是草药
        newOrder.yiZhuLX = 'cy' // 默认设置为草药医嘱类型
        newOrder.xuNiZH = `${baseXuNiZH}-${itemIndex + 1}` // 草药使用特殊组号格式
      }

      // 如果drawerYiZhuList最后一条没有药品，先删掉
      if (this.drawerYiZhuList.length > 0) {
        const lastOrderIndex = this.drawerYiZhuList.length - 1
        const lastOrder = this.drawerYiZhuList[lastOrderIndex]
        // 只有在处理组内第一条数据时才检查并删除末尾空行
        if (itemIndex === 0 && !lastOrder.mingCheng) {
          this.drawerYiZhuList.pop()
          if (this.drawerClickedRowIndex === lastOrderIndex + 1) {
            // 索引变化
            this.drawerClickedRowIndex =
              this.drawerYiZhuList.length > 0 ? this.drawerYiZhuList.length - 1 : null
          }
        }
      }

      // 添加到医嘱列表
      this.drawerYiZhuList.push(newOrder)
      this.drawerClickedRowIndex = this.drawerYiZhuList.length - 1

      // 触发医嘱逻辑检查
      await this.yzLogic({
        row: newOrder,
        drugRow: {
          ...newOrder,
          daiMa: order.yaoPinID,
          yaoPinData: yaoPinData,
          leiXing: newOrder.leiBie || '3'
        }
      })
    },
    // 其他功能下拉框
    handleOtherFunc(command) {
      switch (command) {
        case '1': // 患者医嘱360
          this.openPatientOrder360()
          break
        case '2': // 闭环展示
          this.openClosedLoopDisplay()
          break
        case '3': // 不良事件上报
          this.openAdverseEventReport()
          break
        case '4': // 生成临时医嘱
          this.copyToTempOrders()
          break
      }
    },
    // 虚拟药库下拉框
    handleVirtualDrugFunc(command) {
      switch (command) {
        case '1': // 外配药品
          this.virtualDrugDialog = true
          this.virtualDrugTitle = '外配药品'
          break
        case '2': // 赠送药品
          this.virtualDrugDialog = true
          this.virtualDrugTitle = '赠送药品'
          break
        // case '3': // 外购药品
        //   break
      }
    },
    // 虚拟药库搜索
    handleVirtualDrugSearch() {
      const yaoFangDMs = this.inpatientInit?.yaoFangDMs.map((item) => item.yaoFangDM).join(',')
      getNewWaiGouYP({
        key: this.virtualDrugInput.toUpperCase(),
        yaoFangDMs: yaoFangDMs
      }).then((res) => {
        if (res.hasError === 0) {
          this.virtualDrugList = res.data
        }
      })
    },
    // 虚拟药库选择
    async handleVirtualDrugSelect(row) {
      await this.handleDrugSelect(row, this.virtualDrugList)
      this.virtualDrugDialog = false
    },
    // 中成药分餐选择
    async handleChinesePatentDrugSelect(row) {
      await this.handleDrugSelect(row, this.chinesePatentDrugList)
      this.chinesePatentDrugDialog = false
    },
    // 肠外营养套餐保存成功
    async handleChangWaiImportSuccess() {
      await this.getBingRenYZ()
      await this.getUncommitYZ()
      this.changWaiDialog = false
    },
    // 向上移动
    handleMoveUp(index) {
      if (index > 0) {
        const temp = this.tableConfigList[index]
        this.$set(this.tableConfigList, index, this.tableConfigList[index - 1])
        this.$set(this.tableConfigList, index - 1, temp)
      }
    },

    // 向下移动
    handleMoveDown(index) {
      if (index < this.tableConfigList.length - 1) {
        const temp = this.tableConfigList[index]
        this.$set(this.tableConfigList, index, this.tableConfigList[index + 1])
        this.$set(this.tableConfigList, index + 1, temp)
      }
    },
    // 表格配置保存
    async handleTableConfigSave() {
      try {
        const res = await saveYzBiaoTiGxh({
          ...this.inpatientInit.biaoTiGXH,
          geXingHuaNR: JSON.stringify(this.tableConfigList)
        })

        if (res.hasError === 0) {
          this.$message.success('保存配置成功')

          // 更新初始化信息中的配置
          this.inpatientInit.biaoTiGXH.geXingHuaNR = JSON.stringify(this.tableConfigList)

          // 重新设置表格列
          this.setTableColumns()
          this.tableConfigDialog = false
        } else {
          this.$message.error('保存配置失败：' + (res.message || '未知错误'))
        }
      } catch (error) {
        console.error('保存表格配置时发生错误：', error)
        this.$message.error('保存配置失败，请重试')
      }
    },
    // 表格配置关闭
    handleTableConfigClose() {
      // 还原配置
      if (this.inpatientInit?.biaoTiGXH?.geXingHuaNR) {
        try {
          this.tableConfigList = JSON.parse(this.inpatientInit.biaoTiGXH.geXingHuaNR).filter(
            (config) => config.key !== '煎法' && config.key !== '选择'
          )
        } catch (error) {
          console.error('解析表格配置时发生错误：', error)
          // 如果解析失败，使用默认配置
          this.resetTableConfigToDefault()
        }
      } else {
        // 如果没有保存的配置，使用默认配置
        this.resetTableConfigToDefault()
      }
      this.tableConfigDialog = false
    },
    // 获取原始列配置
    getOriginalColumns() {
      return [
        { value: 'xuNiZH', label: '组号', props: { fixed: true } },
        {
          value: 'zhuangTaiBZMC',
          label: '医嘱状态',
          props: { fixed: true, width: '70' },
          component: 'TableTag'
        },
        { value: 'leiXingMC', label: '医嘱类型', props: { fixed: true, width: '70' } },
        { value: 'kaiShiSJ_rq', label: '开始日期', props: { fixed: true, width: '90' } },
        { value: 'kaiShiSJ_sj', label: '开始时间', props: { fixed: true, width: '50' } },
        { value: 'mingCheng', label: '医嘱名称', props: { fixed: true, width: '240' } },
        { value: 'zuHaoTXT', label: '组', props: { fixed: true, width: '25', align: 'center' } },
        { value: 'yiCiYL', label: '一次用量', props: { fixed: true, width: '65' } },
        { value: 'jiLiangDW', label: '剂量单位', props: { fixed: true, width: '70' } },
        { value: 'zhiXingFF', label: '用法', props: { width: '80' } },
        { value: 'zhiXingPL', label: '频率', props: { width: '80' } },
        { value: 'geiYaoSJ', label: '用药执行时间', props: { width: '100' } },
        { value: 'guiGe', label: '规格/单价', props: { width: '100' } },
        { value: 'yiShengXM', label: '医师姓名', props: { width: '80' } },
        {
          value: 'yiZhuLXMC',
          label: '类别',
          props: {
            columnKey: 'leiBie',
            filterMultiple: false,
            width: '70',
            filters: [
              { text: '药品', value: '1' },
              { text: '饮食', value: '0' },
              { text: '治疗', value: '2' },
              { text: '嘱托', value: '98' },
              { text: '化验', value: '3' },
              { text: '检查', value: '4' },
              { text: '其他', value: '99' }
            ]
          }
        },
        {
          value: 'feiYongLXMC',
          label: '费用类型',
          props: {
            columnKey: 'feiYongLX',
            width: '80',
            filterMultiple: false,
            filters: [
              { text: '自费', value: '0' },
              { text: '甲类', value: '1' },
              { text: '乙类', value: '2' },
              { text: '其他', value: '99' }
            ]
          }
        },
        { value: 'shiFouZBMC', label: '自备', props: { width: '60' } },
        { value: 'jinRiLT', label: '今日临停', props: { width: '70' } },
        { value: 'teShuYF', label: '特殊用法/备注说明', props: { width: '150' } },
        { value: 'bingQuMC', label: '执行病区' },
        { value: 'shouFeiSL', label: '数量/剂数' },
        { value: 'chiXuTS', label: '持续天数' },
        { value: 'yongYaoTS', label: '用药天数' },
        { value: 'danWei', label: '单位' },
        { value: 'shouFeiCS', label: '收费次数' },
        { value: 'jinRiCYMC', label: '预计出院' },
        { value: 'zanShiBQMC', label: '是否代煎' },
        { value: 'yaoPinDSBZ', label: '滴速' },
        { value: 'jieShuSJ', label: '结束时间', props: { width: '90' } },
        { value: 'tongZhiDanIDMC', label: '手术通知单', props: { width: '120' } },
        { value: 'luRuSJ', label: '医嘱时间', props: { width: '90' } },
        { value: 'daoRuRYXM', label: '导入人员' },
        { value: 'daoRuSJ', label: '导入时间', props: { width: '90' } },
        { value: 'yiZhuID', label: '医嘱ID' },
        { value: 'biHuanDZ', label: '闭环', component: 'TableLink' },
        {
          value: 'buLiangFYSB',
          label: '不良反应上报',
          props: { width: '100' },
          component: 'TableLink'
        }
      ]
    },
    // 重置表格配置到默认值
    resetTableConfigToDefault() {
      this.tableConfigList = [
        { show: true, key: '组号' },
        { show: true, key: '医嘱状态' },
        { show: true, key: '医嘱类型' },
        { show: true, key: '开始日期' },
        { show: true, key: '开始时间' },
        { show: true, key: '医嘱名称' },
        { show: true, key: '组' },
        { show: true, key: '一次用量' },
        { show: true, key: '剂量单位' },
        { show: true, key: '用法' },
        { show: true, key: '频率' },
        { show: true, key: '用药执行时间' },
        { show: true, key: '规格/单价' },
        { show: true, key: '医师姓名' },
        { show: true, key: '类别' },
        { show: true, key: '费用类型' },
        { show: true, key: '自备' },
        { show: true, key: '今日临停' },
        { show: true, key: '特殊用法/备注说明' },
        { show: true, key: '执行病区' },
        { show: true, key: '数量/剂数' },
        { show: true, key: '持续天数' },
        { show: true, key: '用药天数' },
        { show: true, key: '单位' },
        { show: true, key: '收费次数' },
        { show: true, key: '预计出院' },
        { show: true, key: '是否代煎' },
        { show: true, key: '滴速' },
        { show: true, key: '结束时间' },
        { show: true, key: '手术通知单' },
        { show: true, key: '医嘱时间' },
        { show: true, key: '导入人员' },
        { show: true, key: '导入时间' },
        { show: true, key: '医嘱ID' },
        { show: true, key: '闭环' },
        { show: true, key: '不良反应上报' }
      ]
    },
    // 设置表格列
    setTableColumns() {
      // 保存原始列配置的副本，避免循环引用问题
      const originalColumns = [...this.getOriginalColumns()]

      // 根据配置设置显示的列
      const visibleColumns = this.tableConfigList.filter((config) => config.show)

      // 重新排序columns数组
      this.columns = visibleColumns.map((config) => {
        // 从原始列配置中查找
        const originalColumn = originalColumns.find((col) => col.label === config.key)

        return (
          originalColumn || {
            value: config.key.toLowerCase(),
            label: config.key
          }
        )
      })

      // 使用防抖处理表格刷新，避免频繁DOM操作
      if (this.tableRefreshTimer) {
        clearTimeout(this.tableRefreshTimer)
      }

      this.tableRefreshTimer = setTimeout(() => {
        this.$nextTick(() => {
          // 强制刷新表格以解决固定列错行问题
          if (this.$refs.standingTable) {
            this.$refs.standingTable.doLayout()
          }
          if (this.$refs.tempTable) {
            this.$refs.tempTable.doLayout()
          }
        })
      }, 100)
    },
    // 个人模板查询
    handleMuBanFilter() {
      if (this.muBanFilter) {
        this.personalMuban = this.personalMuBanTemp.filter((item) => {
          return (
            item.pinYin.includes(this.muBanFilter.toUpperCase()) ||
            item.muBanMC.includes(this.muBanFilter)
          )
        })
      } else {
        this.personalMuban = this.personalMuBanTemp
      }
    },
    // 返回个人模板
    goBackPersonal() {
      this.isDetail = false
      this.personalMuban = this.personalMuBanTemp
    },
    // 个人模板详细
    handlePersonalSelect(row) {
      this.isDetail = true
      this.selectedMuBanMC = row.muBanMC
      this.personalMuBanMingXi = []
      getYaoPinMbDetail({
        bingLiID: this.bingLiID,
        muBanMC: row.muBanMC,
        leiBie: row.leiBie,
        yiZhuLB: row.yiZhuLB
      }).then((res) => {
        if (res.hasError === 0) {
          this.personalMuBanMingXi = res.data
        }
      })
    },
    // 个人模板明细选择
    async handlePersonalMXSelect(row) {
      await this.handleDrugSelect(row, this.personalMuban)
      this.personalDialog = false
    },
    // 常用药模板选择
    async handleChangYongYaoSelect(row) {
      await this.handleDrugSelect(row, this.changYongYaoList)
      this.changYongYaoDialog = false
    },
    handleTangJiMuBanFilter() {
      if (this.muBanFilter) {
        this.tongYongTJMuBan = this.tongYongTJMuBanTemp.filter((item) => {
          return (
            item.pinYin.includes(this.muBanFilter.toUpperCase()) ||
            item.muBanMC.includes(this.muBanFilter)
          )
        })
      } else {
        this.tongYongTJMuBan = this.tongYongTJMuBanTemp
      }
    },
    // 返回汤剂模板列表
    goBackTangJi() {
      this.isDetail = false
      this.tongYongTJMuBan = this.tongYongTJMuBanTemp
    },
    // 汤剂模板选择查看详情
    handleTangJiSelect(row) {
      this.isDetail = true
      this.selectedMuBanMC = row.muBanMC

      // 获取汤剂模板详情
      getYaoPinMbDetail({
        bingLiID: this.bingLiID,
        muBanMC: row.muBanMC,
        leiBie: row.leiBie,
        yiZhuLB: row.yiZhuLB
      }).then((res) => {
        if (res.hasError === 0) {
          this.tongYongTJMuBanMingXi = res.data
        }
      })
    },
    // 汤剂模板明细选择
    async handleTangJiMXSelect(row) {
      await this.handleDrugSelect(row, this.tongYongTJMuBanMingXi)
      this.tongYongTJDialog = false
    },
    // 治疗医嘱模板选择
    async handleZhiLiaoMuBanSelect(selectedRows) {
      if (!selectedRows || selectedRows.length === 0) {
        this.zhiLiaoMubanDialog = false
        return
      }

      // 如果最后一条医嘱存在但没有名称，先删除它
      const index = this.drawerYiZhuList.length - 1
      const lastOrder = index >= 0 ? this.drawerYiZhuList[index] : null

      if (lastOrder && !lastOrder.mingCheng) {
        this.drawerYiZhuList.pop()
        if (this.drawerClickedRowIndex === index) {
          this.drawerClickedRowIndex =
            this.drawerYiZhuList.length > 0 ? this.drawerYiZhuList.length - 1 : null
        }
      }

      // 获取当前时间
      const currentTime = await this.getCurrentTime()

      try {
        // 统一处理所有医嘱
        for (let i = 0; i < selectedRows.length; i++) {
          const row = selectedRows[i]

          // 如果不是第一条医嘱，确保上一条医嘱有名称
          if (i > 0) {
            const prevIndex = this.drawerYiZhuList.length - 1
            const prevOrder = this.drawerYiZhuList[prevIndex]

            if (prevOrder && !prevOrder.mingCheng) {
              this.$set(prevOrder, 'mingCheng', selectedRows[i - 1].yiZhuMLMC)
              this.$set(prevOrder, 'daiMa', String(selectedRows[i - 1].yiZhuMLID))
            }
          }

          // 添加新组
          await this.addGroup()

          // 获取当前行索引
          const currentIndex =
            this.drawerClickedRowIndex !== null
              ? this.drawerClickedRowIndex
              : this.drawerYiZhuList.length - 1

          // 更新当前医嘱数据
          const currentOrder = this.drawerYiZhuList[currentIndex]
          this.$set(currentOrder, 'leiXing', '1')
          this.$set(currentOrder, 'leiBie', '1')
          this.$set(currentOrder, 'yiZhuLX', this.tabActive)
          this.$set(currentOrder, 'kaiShiSJ', currentTime)
          this.$set(currentOrder, 'daiMa', String(row.yiZhuMLID))
          this.$set(currentOrder, 'mingCheng', row.yiZhuMLMC)
          this.$set(currentOrder, 'zhiXingFF', row.zhiXingFF || '')
          this.$set(currentOrder, 'zhiXingPL', row.zhiXingPL || '')
          this.$set(currentOrder, 'geiYaoSJ', row.zhiXingSJ || '')
          this.$set(currentOrder, 'teShuYF', row.teShuSM || '')
          this.$set(currentOrder, 'shouFeiSL', row.shuLiang || '')
          this.$set(currentOrder, 'chiXuTS', row.chiXuTS || '')

          // 执行医嘱逻辑
          await this.yzLogic({
            row: currentOrder,
            drugRow: {
              ...currentOrder
            }
          })
        }
      } catch (error) {
        console.error('导入治疗模板失败:', error)
        this.$message.error('导入治疗模板失败: ' + (error.message || '未知错误'))
      }

      this.zhiLiaoMubanDialog = false
    },
    // 综合医嘱模板选择
    async handleZongHeMuBanSelect(selectedRows) {
      if (!selectedRows || selectedRows.length === 0) {
        this.zongHeMubanDialog = false
        return
      }

      // 如果最后一条医嘱存在但没有名称，先删除它
      const index = this.drawerYiZhuList.length - 1
      const lastOrder = index >= 0 ? this.drawerYiZhuList[index] : null

      if (lastOrder && !lastOrder.mingCheng) {
        this.drawerYiZhuList.pop()
        if (this.drawerClickedRowIndex === index) {
          this.drawerClickedRowIndex =
            this.drawerYiZhuList.length > 0 ? this.drawerYiZhuList.length - 1 : null
        }
      }

      // 获取当前时间
      const currentTime = await this.getCurrentTime()

      try {
        let currentZuHao = ''
        let prevIndex = -1

        for (let i = 0; i < selectedRows.length; i++) {
          const row = selectedRows[i]

          // 如果不是第一条医嘱，确保上一条医嘱有名称
          if (i > 0 && prevIndex >= 0) {
            const prevOrder = this.drawerYiZhuList[prevIndex]

            if (prevOrder && !prevOrder.mingCheng) {
              const prevTemplate = selectedRows[i - 1]
              this.$set(prevOrder, 'mingCheng', prevTemplate.mingCheng)

              // 根据不同类型设置代码
              if (prevTemplate.leiBie === '1' || prevTemplate.leiBie === '2') {
                this.$set(prevOrder, 'daiMa', prevTemplate.xiangMuID.substring(1))
              } else {
                this.$set(prevOrder, 'daiMa', prevTemplate.xiangMuID)
              }
            }
          }

          if (row.leiBie === '1' || row.leiBie === '2' || row.leiBie === '4') {
            // 治疗 饮食 嘱托
            await this.addGroup()

            // 获取当前行索引
            const currentIndex =
              this.drawerClickedRowIndex !== null
                ? this.drawerClickedRowIndex
                : this.drawerYiZhuList.length - 1

            prevIndex = currentIndex

            // 更新当前医嘱数据
            const currentOrder = this.drawerYiZhuList[currentIndex]
            const daiMa =
              row.leiBie === '1' || row.leiBie === '2' ? row.xiangMuID.substring(1) : row.xiangMuID

            this.$set(currentOrder, 'leiXing', row.leiBie)
            this.$set(currentOrder, 'daiMa', daiMa)
            this.$set(currentOrder, 'yiZhuLX', this.tabActive)
            this.$set(currentOrder, 'kaiShiSJ', currentTime)
            this.$set(currentOrder, 'mingCheng', row.mingCheng)
            this.$set(currentOrder, 'zhiXingFF', row.zhiXingFF || '')
            this.$set(currentOrder, 'zhiXingPL', row.zhiXingPL || '')
            this.$set(currentOrder, 'geiYaoSJ', row.geiYaoSJ || '')
            this.$set(currentOrder, 'yiCiYL', row.yiCiYL || '')
            this.$set(currentOrder, 'leiBie', row.leiBie || '')
            this.$set(currentOrder, 'teShuYF', row.teShuYF || '')
            this.$set(currentOrder, 'shouFeiSL', row.shouFeiSL || '')
            this.$set(currentOrder, 'shouFeiCS', row.shouFeiCS || '')
            this.$set(currentOrder, 'chiXuTS', row.chiXuTS || '')

            // 执行医嘱逻辑
            await this.yzLogic({
              row: currentOrder,
              drugRow: {
                ...currentOrder
              }
            })
          } else if (row.leiBie === '3') {
            // 药品
            if (this.tabActive === 'cq' && (row.jiXing === 'Ypj' || row.jiXing === 'Mjkl')) {
              await this.$alert('长期医嘱里不能开草药和颗粒', '提示信息')
              return
            }

            // 组号相同的项走新增项，组号不同的项走新增组
            if (row.zuHao === currentZuHao) {
              await this.add()
            } else {
              await this.addGroup()
              currentZuHao = row.zuHao
            }

            // 获取当前行索引
            const currentIndex =
              this.drawerClickedRowIndex !== null
                ? this.drawerClickedRowIndex
                : this.drawerYiZhuList.length - 1

            prevIndex = currentIndex

            // 更新当前医嘱数据
            const currentOrder = this.drawerYiZhuList[currentIndex]
            this.$set(currentOrder, 'daiMa', row.xiangMuID)
            this.$set(currentOrder, 'yiZhuLX', this.tabActive)
            this.$set(currentOrder, 'kaiShiSJ', currentTime)
            this.$set(currentOrder, 'mingCheng', row.mingCheng)
            this.$set(currentOrder, 'zhiXingFF', row.zhiXingFF || '')
            this.$set(currentOrder, 'zhiXingPL', row.zhiXingPL || '')
            this.$set(currentOrder, 'geiYaoSJ', row.geiYaoSJ || '')
            this.$set(currentOrder, 'yiCiYL', row.yiCiYL || '')
            this.$set(currentOrder, 'leiBie', row.leiBie || '3')
            this.$set(currentOrder, 'teShuYF', row.teShuYF || '')
            this.$set(currentOrder, 'shouFeiSL', row.shouFeiSL || '')
            this.$set(currentOrder, 'shouFeiCS', row.shouFeiCS || '')
            this.$set(currentOrder, 'chiXuTS', row.chiXuTS || '')
            this.$set(currentOrder, 'guiGe', row.guiGe || '')
            this.$set(currentOrder, 'danJia', row.danJia || '')
            this.$set(currentOrder, 'yiCiYL', row.yiCiYL || '')

            const yaoPinData = {
              yaoPinID: row.xiangMuID,
              yaoFangDM: row.yaoFangDM || '',
              jiLiang: row.jiLiang || '',
              yiZhuLB: row.yiZhuLB || '',
              jiXing: row.jiXing || '',
              leiXing: row.leiBie || '3'
            }

            // 执行医嘱逻辑
            await this.yzLogic({
              row: currentOrder,
              drugRow: {
                ...currentOrder,
                yaoPinData: yaoPinData
              }
            })
          }
        }
      } catch (error) {
        console.error('导入综合医嘱模板失败:', error)
        this.$message.error('导入综合医嘱模板失败: ' + (error.message || '未知错误'))
      }

      this.zongHeMubanDialog = false
    },
    // 保存模板选择
    handleSaveMBSelectionChange(selection) {
      const selectedRows = selection
      // 选中组号相同的行
      this.selectedMBRows = this.saveMBList.filter((row) => {
        return selectedRows.some((selectedRow) => selectedRow.zuHao === row.zuHao)
      })
    },
    // 保存模板确定
    async handleSaveMBConfirm() {
      try {
        // 1. 验证选中的医嘱
        if (!this.selectedMBRows.length) {
          await this.$confirm('请选择要保存的医嘱', '提示信息', {
            type: 'info',
            showCancelButton: false
          })
          return
        }

        // 2. 如果是个人模板，验证模板名称
        if (this.saveType === '2') {
          if (!this.templateName) {
            await this.$confirm('请输入模板名称！', '提示信息', {
              type: 'info',
              showCancelButton: false
            })
            return
          }
          if (this.templateName.length > 10) {
            await this.$confirm('模板名称不能大于10个字！', '提示信息', {
              type: 'info',
              showCancelButton: false
            })
            return
          }

          // 检查药品类型是否混合
          let firstOrderType = null
          const hasMultipleTypes = this.selectedMBRows.some((order) => {
            if (firstOrderType === null) {
              firstOrderType = order.yiZhuLB // 医嘱类别：1-西药 2-中成药 3-草药
              return false
            }
            return order.yiZhuLB !== firstOrderType
          })

          if (hasMultipleTypes) {
            await this.$confirm('保存失败，西药、中成药、草药不能混合保存为一个模板', '提示信息', {
              type: 'info',
              showCancelButton: false
            })
            return
          }
        }
        // 3. 调用保存接口
        const res = await saveAsYaoPinMb({
          bingLiID: this.bingLiID,
          zuHaos: this.selectedMBRows.map((row) => row.zuHao),
          leiXing: this.saveType === '1' ? 'cyy' : 'grmb',
          muBanMC: this.saveType === '2' ? this.templateName.trim() : ''
        })

        if (res.hasError === 0) {
          this.$message.success('模板保存成功')
          this.selectedMBRows = []
          this.templateName = ''
          this.saveMBDialog = false
        }
      } catch (error) {
        console.error('保存模板失败:', error)
        this.$message.error(error.message || '保存模板失败，请重试')
      }
    },
    // 保存模板取消
    handleSaveMBCancel() {
      this.selectedMBRows = []
      this.saveMBDialog = false
    },
    // 清理医嘱行的额外数据，保留基础必要字段
    cleanRowExtraData(row) {
      // 定义基础必要字段列表
      const baseFields = [
        'zuHao',
        'xuNiZH',
        'zuHaoTXT',
        'bingLiID',
        'zhuYuanID',
        'yiZhuLX',
        'leiBie',
        'mingCheng',
        'kaiShiSJ',
        'jieShuSJ',
        'yiShengID',
        'bingQuID',
        'yiZhuID',
        'jinRiCY',
        'zanShiBQ'
      ]

      // 获取当前行的所有字段
      const allKeys = Object.keys(row)

      // 删除不在基础字段列表中的额外字段
      allKeys.forEach((key) => {
        if (!baseFields.includes(key)) {
          this.$delete(row, key)
        }
      })
    },

    // 开药判断逻辑
    async yzLogic({ row, drugRow }) {
      try {
        // 在执行yzLogic之前，先清理当前行的额外数据
        this.cleanRowExtraData(row)

        // 判断类型 3-药品 1-治疗 2-饮食 4-嘱托
        if (drugRow.leiXing === '3') {
          // 如果封装前药品ID存在则取封装前药品ID
          const yaoPinID = drugRow.yaoPinData?.fenZhuangQianYPID
            ? drugRow.yaoPinData?.fenZhuangQianYPID
            : drugRow.yaoPinData?.yaoPinID
          const JBXX = await this.getYaoPinJBSYFF(row, drugRow, yaoPinID)
          const FJXX = await this.getYaoPinFJXX(row, drugRow, JBXX)
          // 判断临床路径
          if (this.linChuangLuJing === '1' && this.tabActive === 'cq') {
            // linChuangLuJing不知道哪里获取
          }

          // 重复用药判断 查找可重复药品和溶媒药品
          const cfypItem = FJXX.drugAttributesVos?.find(
            (item) => item.shuXingDM === 'cfyp' && item.shuXingZhi === '1'
          )
          const rmypItem = FJXX.drugAttributesVos?.find(
            (item) => item.shuXingDM === 'rmyp' && item.shuXingZhi === '1'
          )
          if (!cfypItem && !rmypItem) {
            const existOrders = this.yiZhuList.filter(
              (item) =>
                item.xiangMuID === drugRow.daiMa &&
                item.yanSe !== 'red' &&
                item.zhuangTaiBZMC !== '已执行'
            )
            const drawerExistOrders = this.drawerYiZhuList.filter(
              (item) =>
                (item.yaoPinID === drugRow.daiMa || item.xiangMuID === drugRow.daiMa) &&
                item !== row
            )
            if (existOrders.length || drawerExistOrders.length) {
              // 草药提示
              if (drugRow.yaoPinData?.yiZhuLB === '3') {
                await this.$alert(`中药${drugRow.mingCheng}重复`, '信息窗口', {
                  callback: () => {
                    this.handleDeleteDrawerRow(row)
                  }
                })
                return
              } else {
                // 其他药提示
                await this.$alert(`列表里${drugRow.mingCheng}重复`, '信息窗口')
              }
            }
          }
          // 中成药分餐
          const ZCYFC_FLAG = this.isZCYFC(row, drugRow, FJXX)
          if (!ZCYFC_FLAG) {
            this.handleDeleteDrawerRow(row)
          }

          // 耐药性判断
          // 药品ID为96906（》舒普深针(头孢哌酮舒巴)、99077（特治星针(哌拉西林他唑)、4761（美罗培南针(海正美特)）
          if (drugRow.daiMa === '96906' || drugRow.daiMa === '99077' || drugRow.daiMa === '4761') {
            await this.$alert(
              `${drugRow.mingCheng}为遏制细菌耐药，请谨慎选择该种抗菌药物，使用前请明确细菌耐药谱！`,
              '信息窗口'
            )
          }
          // 药品附加信息判断弹窗队列
          const continueProcess = await this.handleAlertQueue(row, FJXX, drugRow)
          if (!continueProcess) {
            return
          }
        } else if (drugRow.leiXing === '1') {
          // 获取治疗附加信息
          const ZLFJXX = await this.getZhiLiaoFJXX(row, drugRow)
          if (ZLFJXX && ZLFJXX.cuoWuDM === '1') {
            this.$message.error(`无法开具治疗医嘱${drugRow.mingCheng}，${ZLFJXX.cuoWuXX}`)
            this.handleDeleteDrawerRow(row)
            return
          }
          await this.checkZhiLiaoYiZhu(row, drugRow, ZLFJXX)
        } else if (drugRow.leiXing === '2') {
          const YSFJXX = await this.getYinShiFJXX(row, drugRow)
          if (YSFJXX && YSFJXX.cuoWuDM === '1') {
            this.$message.error(`${YSFJXX.cuoWuXX}`)
            this.handleDeleteDrawerRow(row)
            return
          }
        }
      } catch (error) {
        console.log(error)
        await this.$alert(`${error.message}`, '信息窗口', {
          callback: () => {
            this.handleDeleteDrawerRow(row)
          }
        })
      }
    },
    // 是否麻醉精神药品
    isMZJSYP(drugAttributesVos) {
      let message = ''
      if (!drugAttributesVos || drugAttributesVos.length === 0) {
        return ''
      }
      // 查找麻醉药品或精神药品标记
      const mzypItem = drugAttributesVos.find(
        (item) => item.shuXingDM === 'maZuiYP' && item.shuXingZhi === '1'
      )
      // 查找控制使用药品标记
      const kzypItem = drugAttributesVos.find(
        (item) => item.shuXingDM === 'kzsy' && item.shuXingZhi === '1'
      )
      if (mzypItem && kzypItem) {
        message =
          '此药为麻醉药品或一类精神药品！进口药、贵重药、用量较大的药品，控制使用。建议选择同类低价药品!'
        return message
      }
      if (mzypItem) {
        message = '此药为麻醉药品或一类精神药品！'
      }

      if (kzypItem) {
        message = '此药为进口药、贵重药、用量较大的药品，控制使用。建议选择同类低价药品!'
      }

      return message
    },
    // 中成药分餐判断
    isZCYFC(row, drugRow, FJXX) {
      const { drugAttributesVos } = FJXX
      // 需分餐中成药
      const cqcyItem = drugAttributesVos.find((item) => item.shuXingDM === 'cqcy')
      // 中成药
      if (drugRow.leiXing === '3' && drugRow.yaoPinData?.yiZhuLB === '2') {
        if (cqcyItem && cqcyItem.shuXingZhi === '0' && row.yiZhuLX !== 'cydy') {
          this.$alert(`[${drugRow.mingCheng}]不是中成药分餐药品，只能开出院带药！`, '信息窗口')
          return false
        }
        // 2-目录外可用
        if (
          cqcyItem &&
          cqcyItem.shuXingZhi === '2' &&
          row.yiZhuLX !== 'ls' &&
          row.yiZhuLX !== 'cydy'
        ) {
          this.$alert(
            `[${drugRow.mingCheng}]是中成药分餐目录外可用药品，只能开临时医嘱或出院带药！`,
            '信息窗口'
          )
          return false
        }
      }
      return true
    },
    // 获取药品基本使用方法
    async getYaoPinJBSYFF(row, drugRow, yaoPinID) {
      const res = await getYaoPinJBSYFF({ yaoPinID: yaoPinID })
      if (res.hasError === 0 && res.data) {
        // 获取药品剂量
        const { yaoPinJL } = res.data
        const jiLiangDWOptions = (yaoPinJL || []).map((item) => {
          return {
            value: item.jiLiangDW,
            label: item.jiLiangDW
          }
        })
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
        drawerTableRef.handleUpdateColumns({
          column: 'jiLiangDW',
          options: jiLiangDWOptions
        })
        // 获取药品用法用量
        const { yongFaYL } = res.data
        let updateData = {
          yaoPinID: yaoPinID,
          mingCheng: drugRow.mingCheng,
          yiCiYL: '',
          changYongLiang: '',
          jiLiangDW: jiLiangDWOptions[0]?.value || '',
          zhiXingFF: '',
          zhiXingPL: this.tabActive === 'ls' ? 'st' : 'qd',
          geiYaoSJ: '',
          guiGe: drugRow.yaoPinData?.jiLiang || '',
          leiBie: drugRow.leiXing,
          yiZhuLB: drugRow.yaoPinData?.yiZhuLB,
          yaoPinData: drugRow.yaoPinData,
          shouJia: drugRow.yaoPinData?.shouJia || ''
        }
        if (yongFaYL && yongFaYL.length > 0) {
          // 下拉选项
          let zhiXingFFOptions = []
          let zhiXingPLOptions = []
          // 默认执行频率、执行方法
          let zhiXingFF = ''
          let zhiXingPL = ''
          // 区分草药医嘱和草药带药
          if (row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy') {
            zhiXingFF = '煎法'
            zhiXingPL = 'bid'
            zhiXingFFOptions = this.inpatientInit.caoYaoYF.map((item) => {
              return {
                value: item.daiMa,
                label: item.mingCheng
              }
            })
            zhiXingPLOptions = this.inpatientInit.caoYaoPL.map((item) => {
              return {
                value: item.daiMa,
                label: item.mingCheng
              }
            })
          } else {
            zhiXingFF = yongFaYL[0].fangFa || ''
            zhiXingPL = yongFaYL[0].pinLu || this.tabActive === 'ls' ? 'st' : 'qd'
            zhiXingFFOptions = this.inpatientInit.feiCaoYaoYYFF.map((item) => {
              return {
                value: item.fangFaDM,
                label: item.fangFaMC
              }
            })
            zhiXingPLOptions = this.inpatientInit.yongYaoPL.map((item) => {
              return {
                value: item.pinLuDM,
                label: item.pinLuMC
              }
            })
          }
          // 找不到默认频率、默认执行方法，则重置为空
          if (!zhiXingFFOptions.find((item) => item.value === zhiXingFF)) {
            zhiXingFF = ''
          }
          if (!zhiXingPLOptions.find((item) => item.value === zhiXingPL)) {
            zhiXingPL = ''
          }

          // 判断值是否有效（不为空、null、undefined）
          const hasValue = (value) => {
            return value !== null && value !== undefined && value !== ''
          }

          // 获取当前行的模板数据
          const templateData = {
            yiCiYL: row.yiCiYL,
            jiLiangDW: row.jiLiangDW,
            zhiXingFF: row.zhiXingFF,
            zhiXingPL: row.zhiXingPL,
            geiYaoSJ: row.geiYaoSJ
          }

          // 构建最终数据，优先使用模板数据，API数据作为备用
          updateData = {
            ...updateData,
            yiCiYL: hasValue(templateData.yiCiYL)
              ? templateData.yiCiYL
              : yongFaYL[0].changYongLiang || '',
            changYongLiang: yongFaYL[0].changYongLiang || '',
            jiLiangDW: hasValue(templateData.jiLiangDW)
              ? templateData.jiLiangDW
              : yongFaYL[0]?.jiLiangDW || '',
            zhiXingFF: hasValue(templateData.zhiXingFF) ? templateData.zhiXingFF : zhiXingFF,
            zhiXingPL: hasValue(templateData.zhiXingPL) ? templateData.zhiXingPL : zhiXingPL,
            geiYaoSJ: hasValue(templateData.geiYaoSJ)
              ? templateData.geiYaoSJ
              : yongFaYL[0].geiYaoSJ || ''
          }
        }
        await drawerTableRef.handleUpdateRow(row, 'mingCheng', updateData)
        return updateData
      }
    },
    // 获取药品附加信息
    async getYaoPinFJXX(row, drugRow, updateData = {}) {
      const res = await getYaoPinFJXX({
        bingLiID: this.bingLiID, //病历ID
        jiLiangDW: updateData.jiLiangDW || '', //计量单位
        yaoFangDM: drugRow.yaoPinData?.yaoFangDM, //药房代码
        yaoPinID: drugRow.yaoPinData?.yaoPinID, //药品ID
        yaoPinLB: drugRow.yaoPinData?.yaoPinLB, //药品类别（0=本院药品，1=外购药品，2=赠送药品）
        yaoPinMC: drugRow.mingCheng, //药品名称
        yiCiYL: updateData.yiCiYL || '', //一次用量
        yiZhuLB: this.tabActive, //医嘱类别（大类，cq长期,ls临时）
        yiZhuLX: row.yiZhuLX, //医嘱类型（cq=长期,ls=临时,cy=草药,zcydy=草药带药,cydy=出院带药）
        zhiXingFF: updateData.zhiXingFF || '', //执行方法
        zhiXingPL: updateData.zhiXingPL || '', //执行频率
        zhuYuanID: this.inpatientInit.inPatientVo.zhuYuanID //住院ID
      })
      if (res.hasError === 0) {
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
        await drawerTableRef.handleUpdateRow(row, 'mingCheng', {
          FJXX: res.data,
          feiYongLXMC: res.data.kongZhiJBMC
        })
        // 药品滴速赋值
        if (res.data?.yaoPinDSVo && res.data?.yaoPinDSVo?.beiZhu) {
          await drawerTableRef.handleUpdateRow(row, 'yaoPinDSBZ', res.data.yaoPinDSVo.beiZhu)
        }
        return res.data
      }
    },
    async handleAlertQueue(row, FJXX, drugRow) {
      const alertQueue = []
      const { drugAttributesVos } = FJXX

      // 收集所有需要提示的消息
      if (FJXX) {
        // 抗菌药物耐药率提示
        if (FJXX.naiYaoLvTS) {
          alertQueue.push({
            message: FJXX.naiYaoLvTS,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 血制品提醒
        if (FJXX.xueZhiPinTX) {
          alertQueue.push({
            message: FJXX.xueZhiPinTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 二甲双胍(guā)提醒
        if (FJXX.erJiaShuangGuaTX) {
          alertQueue.push({
            message: FJXX.erJiaShuangGuaTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 麻醉精神药品提醒
        const mzypMessage = this.isMZJSYP(drugAttributesVos)
        if (mzypMessage) {
          alertQueue.push({
            message: mzypMessage,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 药物过敏提示
        if (FJXX.guoMinTX) {
          alertQueue.push({
            message: FJXX.guoMinTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 高警示药品提醒
        const gwypItem = drugAttributesVos.find(
          (item) => item.shuXingDM === 'gwyp' && item.shuXingZhi === '1'
        )
        if (gwypItem) {
          alertQueue.push({
            message: `${drugRow.mingCheng}是高警示药品`,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 静脉营养药品
        const jpypItem = drugAttributesVos.find(
          (item) =>
            item.shuXingDM === 'jpyp' && (item.shuXingZhi === '1' || item.shuXingZhi === '2')
        )
        if (jpypItem) {
          alertQueue.push({
            message: `须完成"营养风险筛查"（本次住院期间完成即可）`,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 国家采集提示(剩余使用量提示)
        if (FJXX.shengYuSYLTS) {
          alertQueue.push({
            message: FJXX.shengYuSYLTS,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 非中选药品弹出中选药品列表
        if (FJXX.guoJiaCG === '12' || FJXX.guoJiaCG === '13' || FJXX.guoJiaCG === '14') {
          alertQueue.push({
            type: 'zhongXuan', // 中选药品弹框
            title: '中选药品列表',
            drugRow, // 传递当前药品参数
            row // 行数据
          })
        }

        // NIHSS评分表提示 TODO: 需要打开链接
        if (FJXX.needNIHSS === '1') {
          alertQueue.push({
            message: `必须完成美国国立卫生院神经功能缺损评分表(NIHSS)评分后才可开溶栓药`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }

        // 禁止出院带药开抗菌药物
        const kjypItem = drugAttributesVos.find(
          (item) =>
            item.shuXingDM === 'kjyp' &&
            (item.shuXingZhi === '1' || item.shuXingZhi === '2' || item.shuXingZhi === '3')
        )
        if (row.yiZhuLX === 'cydy' && kjypItem) {
          alertQueue.push({
            message: `最后一次抗菌药物医嘱为预防用药，禁止出院带药开具抗菌药物！`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }

        // 呕吐分级提醒
        if (FJXX.zhiOuYPTX) {
          alertQueue.push({
            message: FJXX.zhiOuYPTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 儿童用药提示
        if (FJXX.erTongYYTS) {
          alertQueue.push({
            message: FJXX.erTongYYTS,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 中医辩证信息
        const res = await getZhongYiBZXX({ yaoPinID: drugRow.yaoPinData?.yaoPinID })
        if (
          res.hasError === 0 &&
          res.data &&
          res.data.zhenDuanXX?.length &&
          res.data.zhengZhuangXX?.length
        ) {
          alertQueue.push({
            type: 'zhongYiBZ', // 中医辩证弹框
            title: '中医辩证信息',
            data: res.data
          })
        }

        // 抗肿瘤药物的处方权限
        const kzpjItem = drugAttributesVos.find(
          (item) => item.shuXingDM === 'kzpj' && item.shuXingZhi === '1'
        )
        const kzxjItem = drugAttributesVos.find(
          (item) => item.shuXingDM === 'kzxj' && item.shuXingZhi === '1'
        )
        if (kzpjItem && !this.inpatientInit.puTongJiZLYWQX) {
          alertQueue.push({
            message: `${drugRow.mingCheng}是普通使用级抗肿瘤药物，你没有开具普通级肿瘤药物的权限！药品ID=${drugRow.daiMa}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        } else if (kzxjItem && !this.inpatientInit.xianZhiJiZLYWQX) {
          alertQueue.push({
            message: `${drugRow.mingCheng}是限制使用级抗肿瘤药物，你没有开具限制级肿瘤药物的权限！药品ID=${drugRow.daiMa}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }
        // 毒性药品控制
        if (drugRow.yaoPinData?.guanLiLX === 'D' && !this.inpatientInit.duXingYPCFQX) {
          alertQueue.push({
            message: `对不起，你没有毒性药品处方权限，无法开具药品【${drugRow.mingCheng}】！药品ID=${drugRow.daiMa} 管理类型=${drugRow.yaoPinData?.guanLiLX}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }
        // 麻醉药品控制
        if (
          (drugRow.yaoPinData?.guanLiLX === 'M' || drugRow.yaoPinData?.guanLiLX === '1') &&
          !this.inpatientInit.maZuiCFQX
        ) {
          alertQueue.push({
            message: `对不起，你没有麻醉处方权限，无法开具药品【${drugRow.mingCheng}】！药品ID=${drugRow.daiMa} 管理类型=${drugRow.yaoPinData?.guanLiLX}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }
        // 处方药判断
        const isMaZuiYaoPin =
          drugRow.yaoPinData?.guanLiLX === '1' || drugRow.yaoPinData?.guanLiLX === 'M'
        const isChuYuanDaiYao = row.yiZhuLX === 'cydy'

        // 检查是否需要处方药诊断
        if (isMaZuiYaoPin || isChuYuanDaiYao) {
          try {
            // 获取处方药诊断信息
            const cfzdRes = await getChuFangZD({
              bingLiID: this.bingLiID
            })

            if (cfzdRes.hasError === 0) {
              if (cfzdRes.data.length > 0) {
                alertQueue.push({
                  type: 'chuFangZD', // 处方药诊断弹窗
                  title: '处方诊断',
                  data: cfzdRes.data,
                  drugRow
                })
              } else {
                alertQueue.push({
                  message: `未获取到大病历四或五的临床诊断！`,
                  title: '信息窗口',
                  type: 'delete' // 提示后删除
                })
              }
            }
          } catch (error) {
            console.error('获取处方药诊断失败:', error)
          }
        }

        // 麻醉药选择用途
        if (drugRow.yaoPinData?.guanLiLX === 'M') {
          const mzytRes = await getMaZuiYT()
          if (mzytRes.hasError === 0 && res.data?.length) {
            alertQueue.push({
              type: 'mzyt', // 麻醉药品用途弹框
              title: '麻醉药品用途',
              data: res.data
            })
          } else {
            alertQueue.push({
              message: `未获取到麻醉药品用途！`,
              title: '信息窗口',
              type: 'delete' // 提示后删除
            })
          }
        }
        // 药品审批
        if (FJXX.sheBaoSP) {
          if (FJXX.sheBaoSP.xiangDingFW?.length) {
            // 新的审批弹窗
            alertQueue.push({
              type: 'sheBaoSP',
              dialogType: 'new',
              title: '麻醉药品用途',
              data: FJXX.sheBaoSP,
              shangCiXdfw: FJXX.shangCiXdfw,
              drugRow
            })
          } else if (FJXX.sheBaoSP.type !== '0') {
            //  老的审批弹窗
            alertQueue.push({
              type: 'sheBaoSP',
              dialogType: 'old',
              title: '麻醉药品用途',
              data: FJXX.sheBaoSP,
              shangCiXdfw: FJXX.shangCiXdfw,
              drugRow
            })
          }
        }
        // 抗菌药物限制
        const kjypItem1 = drugAttributesVos.find(
          (item) => item.shuXingDM === 'kjyp' && item.shuXingZhi === '1'
        )
        const kjypItem2 = drugAttributesVos.find(
          (item) => item.shuXingDM === 'kjyp' && item.shuXingZhi === '2'
        )
        const kjypItem3 = drugAttributesVos.find(
          (item) => item.shuXingDM === 'kjyp' && item.shuXingZhi === '3'
        )
        if (kjypItem1 || kjypItem2 || kjypItem3) {
          // 判断工种代码
          let tygzdm = '' // 通用工种代码
          let tygzmc = '' // 职位名称

          // 获取医生工种（职称）
          const gzdm = this.inpatientInit.gongZhongDM

          // 判断工种代码对应的级别
          switch (gzdm) {
            case '0010': // 市级名中医参照主任医师的抗生素级别
            case '0011': // 主任医师
            case '0381': // 主任中医师
              tygzdm = '0011' // 主任医师
              tygzmc = '主任医师'
              break
            case '0382': // 副主任中医师
            case '0012': // 副主任医师
              tygzdm = '0012' // 副主任医师
              tygzmc = '副主任医师'
              break
            case '0383': // 主治中医师
            case '0013': // 主治医师
              tygzdm = '0013' // 主治医师
              tygzmc = '主治医师'
              break
            case '0384': // 中医师
            case '0014': // 住院医师
              tygzdm = '0014' // 住院医师
              tygzmc = '住院医师'
              break
            case '0385': // 中医士
            case '0015': // 医士
              tygzdm = '0015' // 医士 级别5
              tygzmc = '医士'
              break
            default:
              tygzdm = gzdm
              break
          }

          // 工种代码未设定
          if (tygzdm === '' || tygzdm === '0000') {
            await this.$confirm('工种代码未设定，无法开具抗生素类药品！', '信息窗口', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            })
            this.handleDeleteDrawerRow(row)
            return false
          }

          // 非限制使用级抗菌药物，各级医师都可以开
          if (kjypItem1) {
            alertQueue.push({
              type: 'kangJunYp', // 抗菌药物使用方法弹框
              title: '抗菌药物使用方法',
              row,
              drugRow
            })
          } else if (kjypItem2) {
            // 限制使用级抗菌药物
            // 判断是否有权限开具限制使用级抗菌药物
            // 副高及以上职称
            // 无副高及以上职称的科室，科室主任职务
            // 特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，中级及以上职称
            const isFuGao = tygzdm === '0012' || tygzdm === '0011'
            const isZhuRen =
              this.inpatientInit.existFuGaoZC === '0' && this.inpatientInit.iszhiXingZR === '1'
            const isTeShuKeShi = this.inpatientInit.ganRanZKBZ === '1' && tygzdm === '0013'

            if (isFuGao || isZhuRen || isTeShuKeShi) {
              alertQueue.push({
                type: 'kangJunYp', // 抗菌药物使用方法弹框
                title: '抗菌药物使用方法',
                row,
                drugRow
              })
            } else {
              // 无权限
              alertQueue.push({
                message: `您的职称（${tygzmc}）无法开具限制使用级抗菌药物。根据规定，副高及以上职称才能开具限制使用级抗菌药物！`,
                title: '信息窗口',
                type: 'delete' // 提示后删除
              })
            }
          } else if (kjypItem3) {
            // 特殊使用级抗菌药物
            if (row.yiZhuLX === 'cq') {
              // 长期医嘱
              // 正高级职称
              // 无正高职称医师的科室，科室主任职务
              // 特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，副高及以上职称
              const isZhengGao = tygzdm === '0011'
              const isZhuRen =
                this.inpatientInit.existZhengGaoZC === '0' && this.inpatientInit.iszhiXingZR === '1'
              const isTeShuKeShi = this.inpatientInit.ganRanZKBZ === '1' && tygzdm === '0012'

              if (isZhengGao || isZhuRen || isTeShuKeShi) {
                // 检查特殊抗菌药物会诊单ID
                if (FJXX.teShuKjywHzdID === 0) {
                  alertQueue.push({
                    message: '请先进行特殊类抗生素药物会诊单会诊通过后再进行医嘱的下达。',
                    title: '信息窗口',
                    type: 'delete' // 提示后删除
                  })
                } else {
                  alertQueue.push({
                    type: 'kangJunYp', // 抗菌药物使用方法弹框
                    title: '抗菌药物使用方法',
                    row,
                    drugRow
                  })
                }
              } else {
                // 无权限
                alertQueue.push({
                  message: `您的职称（${tygzmc}）无法开具特殊使用级抗菌药物。长期医嘱中，正高级职称才能开具特殊使用级抗菌药物！`,
                  title: '信息窗口',
                  type: 'delete' // 提示后删除
                })
              }
            } else {
              // 临时医嘱
              // 副高及以上职称
              // 无副高及以上职称的科室，科室主任职务
              // 特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，中级及以上职称
              const isFuGao = tygzdm === '0012' || tygzdm === '0011'
              const isZhuRen =
                this.inpatientInit.existFuGaoZC === '0' && this.inpatientInit.iszhiXingZR === '1'
              const isTeShuKeShi = this.inpatientInit.ganRanZKBZ === '1' && tygzdm === '0013'
              const isJiZhen = String(this.curBrZhuanKeID) === '49' // 急诊抢救科室ID

              if (isFuGao || isZhuRen || isTeShuKeShi || isJiZhen) {
                // 检查是否已有会诊单
                if (FJXX.teShuKjywHzdID === 0) {
                  // TODO:打开会诊单弹窗
                } else {
                  alertQueue.push({
                    type: 'kangJunYp', // 抗菌药物使用方法弹框
                    title: '抗菌药物使用方法',
                    row,
                    drugRow
                  })
                }
              } else {
                // 无权限
                alertQueue.push({
                  message: `您的职称（${tygzmc}）无法开具特殊使用级抗菌药物。临时医嘱中，副高及以上职称才能开具特殊使用级抗菌药物！`,
                  title: '信息窗口',
                  type: 'delete' // 提示后删除
                })
              }
            }
          }
        }
        // TODO：特治特药备案 暂时不做
        // TODO：特殊病备案 暂时不做
      }

      // 递归显示消息队列
      const showNextAlert = async (index = 0) => {
        if (index >= alertQueue.length) return true

        const alertItem = alertQueue[index]
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp

        try {
          switch (alertItem.type) {
            case 'notify': // 仅提示
              await this.$confirm(alertItem.message, alertItem.title, {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              })
              return await showNextAlert(index + 1)

            // case 'confirm': // 需要操作
            //   try {
            //     await this.$confirm(alertItem.message, alertItem.title, alertItem.options)
            //     return await showNextAlert(index + 1)
            //   } catch (error) {
            //     this.handleDeleteDrawerRow(row)
            //     return false
            //   }

            case 'delete': // 提示后删除跳出循环
              await this.$confirm(alertItem.message, alertItem.title, {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              })
              this.handleDeleteDrawerRow(row)
              return false

            case 'zhongXuan': // 中选药品弹框
              try {
                const res = await getZhongXuanYaoPinList({
                  feiZhongXuanYPID: alertItem.drugRow.daiMa,
                  yaoFangDM: alertItem.drugRow.yaoPinData?.yaoFangDM
                })
                if (res.hasError === 0) {
                  this.zhongXuanYaoPinList = res.data
                  this.zhongXuanYaoPinData = {
                    row: alertItem.row,
                    drugRow: alertItem.drugRow
                  }
                  this.zhongXuanDialog = true

                  // 等待弹窗关闭
                  return new Promise((resolve) => {
                    // 创建一次性事件监听器
                    const handler = (flag) => {
                      if (flag) {
                        // 用户选择了药品
                        resolve(false)
                      } else {
                        // 用户只是关闭了弹窗
                        resolve(showNextAlert(index + 1))
                      }
                    }
                    this.$once('zhongXuanDialogSelect', handler)
                  })
                }
                return await showNextAlert(index + 1)
              } catch (error) {
                console.error(error)
                return false
              }
            case 'zhongYiBZ': // 中医辩证
              this.showZhongYiBZDialog(alertItem.data)
              // 等待用户操作
              return new Promise((resolve) => {
                const handler = (result) => {
                  if (result) {
                    // 用户完成中医辩证
                    drawerTableRef.handleUpdateRow(
                      row,
                      'zhongChengYaoZD',
                      result.zhongChengYaoZD.toString()
                    )
                    drawerTableRef.handleUpdateRow(
                      row,
                      'zhongChengYaoZZ',
                      result.zhongChengYaoZZ.toString()
                    )
                    resolve(showNextAlert(index + 1))
                  } else {
                    // 中医取消中医辩证
                    this.handleDeleteDrawerRow(row)
                    resolve(false)
                  }
                }
                this.$once('zhongYiBZDialogSelect', handler)
              })
            case 'mzyt': // 麻醉用途
              this.mzytList = alertItem.data
              this.mzytDialog = true
              // 等待用户操作
              return new Promise((resolve) => {
                const handler = (result) => {
                  if (result) {
                    // 用户保存麻醉用途
                    drawerTableRef.handleUpdateRow(row, 'maZuiYT', result)
                    resolve(showNextAlert(index + 1))
                  } else {
                    // 用户关闭弹窗
                    this.handleDeleteDrawerRow(row)
                    resolve(false)
                  }
                }
                this.$once('mzytSelect', handler)
              })
            case 'sheBaoSP': // 社保审批
              if (isPlainObject(alertItem.data)) {
                this.displayList = [alertItem.data]
              } else if (isArray(alertItem.data)) {
                this.displayList = alertItem.data
              }
              this.sheBaoYaoPinData = alertItem.drugRow
              this.dialogType = alertItem.dialogType
              this.shangCiXdfw = alertItem.shangCiXdfw
              this.sheBaoDialog = true
              // 等待用户操作
              return new Promise((resolve) => {
                const handler = (result) => {
                  if (result) {
                    // 用户保存
                    drawerTableRef.handleUpdateRow(row, 'ziFei', result)
                    resolve(showNextAlert(index + 1))
                  } else {
                    // 用户关闭弹窗
                    this.handleDeleteDrawerRow(row)
                    resolve(false)
                  }
                }
                this.$once('sheBaoSelect', handler)
              })
            case 'kangJunYp': // 抗菌药物使用方法
              const zhiLiaoSYYPIDList = []
              // 收集治疗使用药品ID列表
              this.drawerYiZhuList.forEach((item) => {
                if (item.kangJunYaoYfVo?.shiYongFF === '2') {
                  zhiLiaoSYYPIDList.push(item.yaoPinID ? item.yaoPinID : item.xiangMuID)
                }
              })
              // 调用抗菌药物使用方法弹窗
              return new Promise(async (resolve) => {
                try {
                  // 调用接口获取抗菌药物管理数据
                  const res = await getKangJunYpGl({
                    bingLiID: this.bingLiID,
                    yaoPinID: alertItem.drugRow.daiMa,
                    zhiLiaoSYYPIDList: zhiLiaoSYYPIDList // 治疗使用药品ID列表
                  })

                  if (res.hasError === 0 && res.data) {
                    // 设置弹窗数据
                    this.kangJunYpData = {
                      shiYongFF: res.data.shiYongFF || [],
                      qieKouLB: res.data.qieKouLB || [],
                      yongFaFL: res.data.yongFaFL || [],
                      shuQianSY: res.data.shuQianSY || [],
                      drugAttributesVos: res.data.drugAttributesVos || []
                    }

                    // 重置表单数据
                    this.kangJunYpForm = {
                      shiYongFFValue: '',
                      qieKouLBValue: '',
                      yongFaFLValue: '',
                      shuQianSYValue: '',
                      shouShuMC: ''
                    }
                    // 保存当前处理的行数据
                    this.currentKangJunYpRow = alertItem.row
                    this.currentKangJunYpDrugRow = alertItem.drugRow

                    // 显示弹窗
                    this.kangJunYpDialog = true

                    // 等待用户操作
                    const handler = (result) => {
                      if (result) {
                        // 用户确认选择
                        const kangJunYaoYfVo = {
                          yiZhuID: '',
                          shiYongYY: '',
                          tingZhiSJ: '',
                          ...result
                        }
                        drawerTableRef.handleUpdateRow(row, 'kangJunYaoYfVo', kangJunYaoYfVo)
                        resolve(showNextAlert(index + 1))
                      } else {
                        // 用户取消
                        this.handleDeleteDrawerRow(alertItem.row)
                        resolve(false)
                      }
                    }
                    this.$once('kangJunYpSelect', handler)
                  } else {
                    this.$message.error('获取抗菌药物使用方法数据失败')
                    this.handleDeleteDrawerRow(alertItem.row)
                    resolve(false)
                  }
                } catch (error) {
                  console.error('获取抗菌药物使用方法数据失败', error)
                  this.$message.error('获取抗菌药物使用方法数据失败')
                  this.handleDeleteDrawerRow(alertItem.row)
                  resolve(false)
                }
              })
            case 'chuFangZD': // 处方药诊断
              // 直接使用接口返回的数据
              this.chuFangZDList = alertItem.data || [] // 设置处方诊断列表
              this.chuFangZDDialog = true

              // 等待用户操作
              return new Promise((resolve) => {
                const handler = (result) => {
                  if (result && Array.isArray(result)) {
                    // 用户选择了确定
                    drawerTableRef.handleUpdateRow(row, 'chuFangZDList', result)
                    resolve(showNextAlert(index + 1))
                  } else {
                    // 用户关闭弹窗
                    this.handleDeleteDrawerRow(row)
                    resolve(false)
                  }
                }
                this.$once('chuFangZDSelect', handler)
              })
            default:
              return await showNextAlert(index + 1)
          }
        } catch (error) {
          // 用户关闭弹窗，继续显示下一个
          return await showNextAlert(index + 1)
        }
      }

      // 开始显示消息队列
      return await showNextAlert()
    },

    // 使用方法选择变化
    handleShiYongFFChange() {
      // 如果选择的不是预防选项，清空预防相关的选择
      if (this.kangJunYpForm.shiYongFFValue !== '1') {
        this.kangJunYpForm.qieKouLBValue = ''
        this.kangJunYpForm.yongFaFLValue = ''
        this.kangJunYpForm.shuQianSYValue = ''
      }
      // 选择治疗
      if (this.kangJunYpForm.shiYongFFValue === '2') {
        // 调用感染诊断接口
        getGanRanZD({ bingLiID: this.bingLiID }).then((res) => {
          if (res.hasError === 0) {
            this.ganRanZDList = res.data
            if (this.ganRanZDList.length > 0) {
              // 弹出感染诊断选择
              this.ganRanZDSelectDialog = true
            } else {
              // 弹出感染诊断库
              this.ganRanZDLibraryDialog = true
            }
          }
        })
      }
    },
    // 感染诊断选择
    handleGanRanZDSelected(row) {},
    // 撤销执行弹窗确认
    async handleCancelExecuteConfirm() {
      // 验证理由是否为空
      if (!this.cancelExecuteReason || this.cancelExecuteReason.trim() === '') {
        this.$message.error('撤销执行理由不能为空')
        return
      }

      // 验证理由长度
      if (this.cancelExecuteReason.length > 50) {
        this.$message.error('撤销执行理由长度不能超过50个字符')
        return
      }

      try {
        // 准备接口参数
        const yiZhuIDList = this.selectedCancelRows.map((row) => ({
          yiZhuID: row.yiZhuID
        }))

        const params = {
          yiZhuIDList: yiZhuIDList,
          bingLiID: this.bingLiID,
          zhuYuanID: this.zhuYuanID,
          cheXiaoLY: this.cancelExecuteReason.trim()
        }

        // 调用撤销执行接口
        const res = await cheXiaoZxByYs(params)

        if (res.hasError === 0) {
          this.$message.success('撤销执行成功')
          // 关闭弹窗
          this.cancelExecuteDialog = false
          // 清空数据
          this.selectedCancelRows = []
          this.cancelExecuteReason = ''
          // 刷新医嘱列表
          await this.getBingRenYZ()
        } else {
          this.$message.error(res.message || '撤销执行失败')
        }
      } catch (error) {
        console.error('撤销执行接口调用失败:', error)
        this.$message.error(error.message || '撤销执行失败')
      }
    },

    // 撤销执行弹窗取消
    handleCancelExecuteCancel() {
      this.cancelExecuteDialog = false
      this.selectedCancelRows = []
      this.cancelExecuteReason = ''
    },

    // 切口类别选择变化
    async handleQieKouLBChange(value) {
      // 获取当前选中医嘱的药品ID
      const yaoPinID = this.currentKangJunYpDrugRow?.yaoPinData?.yaoPinID

      // 特定药品只能用于新生儿预防用药的判断
      if ((yaoPinID === '118711' || yaoPinID === '96014') && value !== '4') {
        await this.$alert(
          '(泰司汀)注射用头孢他啶[基]和头孢他啶针(复达欣)[基]只能用于新生儿预防用药！',
          '信息窗口',
          {
            confirmButtonText: '确定',
            type: 'info'
          }
        )
        this.kangJunYpForm.qieKouLBValue = ''
        return
      }

      switch (value) {
        case '1':
          // I类手术切口预防使用
          // 头孢曲松不能用于Ⅰ类切口的围手术期预防用药
          // if (yaoPinID === '96013' || yaoPinID === '4531') {
          //   await this.$alert('该药不能用于Ⅰ类切口的围手术期预防用药', '信息窗口', {
          //     confirmButtonText: '确定',
          //     type: 'info'
          //   })
          //   this.kangJunYpForm.qieKouLBValue = ''
          //   return
          // }

          await this.$alert('一类切口24小时停药!', '信息窗口', {
            confirmButtonText: '确定',
            type: 'info'
          })
          // 弹出高危因素选择框
          const res = await initQingJieSsGwYs()
          if (res.hasError === 0) {
            this.gaoWeiYSList = res.data?.gaoWeiYSList || []
            this.gaoWeiYSDialog = true
          }
          break
        case '4':
          // 新生儿预防用药
          // 弹出新生儿抗生素预防使用问卷
          this.newbornQuestionnaireDialog = true
          break
        case '2':
        case '9':
        default:
          break
      }
    },
    // 用法分类选择变化
    async handleYongFaFLChange(value) {
      if (!this.kangJunYpForm.qieKouLBValue) {
        await this.$alert('请先选择切口类别', '信息窗口', {
          confirmButtonText: '确定',
          type: 'info'
        })
        this.kangJunYpForm.yongFaFLValue = ''
        return
      }
      if (value === '6' || value === '7') {
        if (this.kangJunYpForm.qieKouLBValue !== '3') {
          await this.$alert('操作类预防使用请选择“侵入性操作预防”', '信息窗口', {
            confirmButtonText: '确定',
            type: 'info'
          })
          this.kangJunYpForm.yongFaFLValue = ''
          return
        }
      } else {
        if (this.kangJunYpForm.qieKouLBValue === '3') {
          await this.$alert('非操作类预防使用请不要选择“侵入性操作预防”', '信息窗口', {
            confirmButtonText: '确定',
            type: 'info'
          })
          this.kangJunYpForm.yongFaFLValue = ''
          return
        }
      }
      if (value === '1') {
        // 判断是否一类手术
        const res = await getShiFouYLSS({ bingLiID: this.bingLiID })
        if (res.hasError === 0 && res.data.length > 0) {
          this.yiLeiSSList = res.data.map((item) => {
            // 手术名称用分号连接
            const shouShuMC = item.shouShuXM.map((ss) => ss.shouShuMC).join('; ')
            return `${item.shouShuKSSJ || ''} ${shouShuMC}; ${item.kaiDanSJ || ''}`
          })
          if (this.yiLeiSSList.length > 0) {
            this.kangJunYpForm.shouShuMC = this.yiLeiSSList[this.yiLeiSSList.length - 1]
          }
          this.kangJunYpForm.shuQianSYValue = '1'
        } else {
          await this.$confirm(
            '未找到病人的手术相关信息,不允许在[术前围术期使用]或[术后围术期使用]使用抗菌药物,是否重新选择?',
            '信息窗口',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'info'
            }
          )
          this.kangJunYpForm.yongFaFLValue = ''
          this.kangJunYpForm.shuQianSYValue = ''
        }
      } else {
        this.kangJunYpForm.shuQianSYValue = ''
      }
    },

    // 高危因素弹窗确认
    async handleGaoWeiYSConfirm() {
      // 验证是否选择了高危因素
      if (this.selectedGaoWeiYS.length === 0) {
        await this.$alert('请选择高危因素', '信息窗口', {
          confirmButtonText: '确定',
          type: 'info'
        })
        return
      }
      // 验证是否科室正主任签名
      if (!this.keShiZRMC || !this.secondaryPasswordUserData) {
        await this.$alert('请科室正主任签名', '信息窗口', {
          confirmButtonText: '确定',
          type: 'info'
        })
        return
      }

      try {
        // 关闭弹窗 自动选择感染高危因素
        this.gaoWeiYSDialog = false
        this.isGaoWeiYS = true
        this.kangJunYpForm.yongFaFLValue = '4'
      } catch (error) {
        console.error('保存高危因素选择失败:', error)
        this.$message.error('保存高危因素选择失败')
      }
    },

    // 高危因素弹窗取消
    async handleGaoWeiYSCancel() {
      if (this.selectedGaoWeiYS.length === 0) {
        await this.$alert('请选择高危因素', '信息窗口', {
          confirmButtonText: '确定',
          type: 'info'
        })
      }
      this.gaoWeiYSDialog = false
      this.isGaoWeiYS = false
      this.selectedGaoWeiYS = []
      this.keShiZRMC = ''
      this.keShiZRQMSJ = ''
      // 清空二次签名数据
      this.secondaryPasswordUserData = null
      // 重置切口类别选择
      this.kangJunYpForm.qieKouLBValue = ''
    },

    // 初始化微生物送检选项
    async initWeiShengWuOptions() {
      try {
        const res = await initWeiShengWuSjQkXx()
        if (res.hasError === 0) {
          this.weiShengWuOptions = res.data
        }
      } catch (error) {
        console.error('初始化微生物送检选项失败:', error)
        // 使用默认选项
        this.weiShengWuOptions = [
          {
            daiMa: '1',
            mingCheng: '患者有样可采，请开具微生物送检医嘱并采集条码，之后方可开药'
          },
          {
            daiMa: '2',
            mingCheng: '患者无样可采，请说明理由并做好病程记录备查'
          },
          {
            daiMa: '3',
            mingCheng: '已送检PCT/IL-6/G实验/GM试验，已在抗菌药物开具前完成采集（必须已刷采集条码）'
          }
        ]
      }
    },

    // 打开微生物送检情况弹窗
    async openWeiShengWuDialog() {
      await this.initWeiShengWuOptions()
      this.weiShengWuForm = {
        selectedOption: '',
        beiZhu: ''
      }
      this.weiShengWuDialog = true
    },

    // 微生物送检情况确认
    handleWeiShengWuConfirm() {
      if (!this.weiShengWuForm.selectedOption) {
        this.$message.warning('请选择微生物送检情况')
        return
      }

      // 如果选择的是"患者无样可采"，需要验证备注
      if (this.weiShengWuForm.selectedOption === '2' && !this.weiShengWuForm.beiZhu.trim()) {
        this.$message.warning('请说明理由')
        return
      }

      // 构造返回数据
      const result = {
        selectedOption: this.weiShengWuForm.selectedOption,
        beiZhu: this.weiShengWuForm.beiZhu
      }

      // 触发事件，传递选择结果
      this.$emit('weiShengWuSelect', result)
      this.weiShengWuDialog = false
    },

    // 微生物送检情况取消
    handleWeiShengWuCancel() {
      this.$emit('weiShengWuSelect', false)
      this.weiShengWuDialog = false
    },

    // 二次签名校验
    async handleApprovalClick() {
      this.secondaryPasswordVisible = true
    },

    // 二次签名确认处理
    handleSecondaryPasswordConfirm(userData) {
      try {
        if (userData && userData.yongHuXM) {
          // 保存用户数据
          this.secondaryPasswordUserData = userData
          // 将用户姓名填入科室主任名称字段
          this.keShiZRMC = userData.yongHuXM
          // 保存签名时间
          this.keShiZRQMSJ = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
          // 关闭弹窗
          this.secondaryPasswordVisible = false
        } else {
          this.$message.error('获取用户信息失败，请重试')
        }
      } catch (error) {
        console.error('处理二次签名确认失败:', error)
        this.$message.error('处理二次签名确认失败')
      }
    },

    // 二次签名取消处理
    handleSecondaryPasswordCancel() {
      try {
        // 关闭弹窗
        this.secondaryPasswordVisible = false
        // 清空相关数据
        this.secondaryPasswordUserData = null
      } catch (error) {
        console.error('处理二次签名取消失败:', error)
      }
    },

    // 抗菌药物使用方法弹窗确认
    handleKangJunYpConfirm() {
      // 验证必填项
      if (!this.kangJunYpForm.shiYongFFValue) {
        this.$message.warning('请选择使用方法')
        return
      }

      // 如果选择了预防，验证预防相关选项
      if (this.kangJunYpForm.shiYongFFValue === '1') {
        if (!this.kangJunYpForm.qieKouLBValue) {
          this.$message.warning('请选择切口类别')
          return
        }
        if (!this.kangJunYpForm.yongFaFLValue) {
          this.$message.warning('请选择用法分类')
          return
        }
      }

      // 处理确认逻辑
      // 从this.kangJunYpData.drugAttributesVos中获取对应属性代码的属性值
      const getAttributeValue = (shuXingDM) => {
        const item = this.kangJunYpData.drugAttributesVos.find(
          (item) => item.shuXingDM === shuXingDM
        )
        return item ? item.shuXingZhi : ''
      }

      const kunJunYpResult = {
        kangJunFL: getAttributeValue('kjyp'),
        touBaoJB: getAttributeValue('tbjb'),
        fuHeKJ: getAttributeValue('fhkj'),
        fuKuiNT: getAttributeValue('fknt'),
        shiYongFF: this.kangJunYpForm.shiYongFFValue,
        qieKouLB: this.kangJunYpForm.qieKouLBValue,
        yongYaoFL: this.kangJunYpForm.yongFaFLValue,
        shuQianSYFL: this.kangJunYpForm.shuQianSYValue || '', // 术前使用可以为空
        qingJieSS: this.isGaoWeiYS ? '1' : '',
        qingJieSSGWYs: this.isGaoWeiYS ? this.selectedGaoWeiYS.join('^') : '',
        qingJieSSSPYSID: this.isGaoWeiYS ? this.secondaryPasswordUserData?.yongHuID : '',
        qingJieSSSPYJ: this.isGaoWeiYS ? '1' : '',
        qingJieSSSPSJ: this.isGaoWeiYS ? this.keShiZRQMSJ : '',
        shouShuMC: this.kangJunYpForm.shouShuMC,
        shiFouYWCG: this.newbornQuestionnaireForm.shiFouYWCG,
        weiChanGWYSS:
          this.newbornQuestionnaireForm.weiChanGWYSS.length > 0
            ? this.newbornQuestionnaireForm.weiChanGWYSS.join('^')
            : '',
        qiTaWCGW: this.newbornQuestionnaireForm.qiTaWCGW,
        shiFouYLCBX: this.newbornQuestionnaireForm.shiFouYLCBX,
        linChuangBX:
          this.newbornQuestionnaireForm.linChuangBX.length > 0
            ? this.newbornQuestionnaireForm.linChuangBX.join('^')
            : ''
      }

      // 触发事件，通知消息队列处理完成
      this.$emit('kangJunYpSelect', kunJunYpResult)

      this.kangJunYpDialog = false
    },

    // 抗菌药物使用方法弹窗取消
    handleKangJunYpCancel() {
      this.kangJunYpDialog = false
      this.currentKangJunYpRow = null
      this.currentKangJunYpDrugRow = null

      // 重置表单数据
      this.kangJunYpForm = {
        shiYongFFValue: '',
        qieKouLBValue: '',
        yongFaFLValue: '',
        shuQianSYValue: '',
        shouShuMC: ''
      }

      // 触发事件，通知消息队列处理取消
      this.$emit('kangJunYpSelect', false)
    },

    // 新生儿抗生素预防使用问卷弹窗确认
    async handleNewbornQuestionnaireConfirm() {
      // 验证必填项
      if (!this.newbornQuestionnaireForm.shiFouYWCG) {
        await this.$alert('请选择是否具有围产高危因素！', '信息窗口', {
          confirmButtonText: '确定',
          type: 'info'
        })
        return
      } else if (this.newbornQuestionnaireForm.shiFouYWCG === '1') {
        if (
          this.newbornQuestionnaireForm.weiChanGWYSS.length === 0 &&
          !this.newbornQuestionnaireForm.qiTaWCGW.trim()
        ) {
          await this.$alert('请选择围产高危因素！', '信息窗口', {
            confirmButtonText: '确定',
            type: 'info'
          })
          return
        }
      }

      if (!this.newbornQuestionnaireForm.shiFouYLCBX) {
        await this.$alert('请选择是否具有临床表现！', '信息窗口', {
          confirmButtonText: '确定',
          type: 'info'
        })
        return
      } else if (this.newbornQuestionnaireForm.shiFouYLCBX === '1') {
        if (this.newbornQuestionnaireForm.linChuangBX.length === 0) {
          await this.$alert('请选择临床表现！', '信息窗口', {
            confirmButtonText: '确定',
            type: 'info'
          })
          return
        }
      }

      // 关闭弹窗
      this.newbornQuestionnaireDialog = false

      // 重置表单
      this.newbornQuestionnaireForm = {
        shiFouYWCG: '',
        shiFouYLCBX: '',
        weiChanGWYSS: [],
        linChuangBX: [],
        qiTaWCGW: ''
      }
    },

    // 新生儿抗生素预防使用问卷弹窗取消
    async handleNewbornQuestionnaireCancel() {
      await this.$alert('未完成问卷！', '信息窗口', {
        confirmButtonText: '确定',
        type: 'info'
      })
      this.newbornQuestionnaireDialog = false
      // 重置表单
      this.newbornQuestionnaireForm = {
        shiFouYWCG: '',
        shiFouYLCBX: '',
        weiChanGWYSS: [],
        linChuangBX: [],
        qiTaWCGW: ''
      }
      this.kangJunYpForm.qieKouLBValue = ''
    },

    // 围产高危因素变化处理
    handleQuestion1Change(value) {
      if (value !== '1') {
        // 清空checkbox选择和输入框
        this.newbornQuestionnaireForm.weiChanGWYSS = []
        this.newbornQuestionnaireForm.qiTaWCGW = ''
      }
    },

    // 临床表现变化处理
    handleQuestion2Change(value) {
      if (value !== '1') {
        // 清空checkbox选择
        this.newbornQuestionnaireForm.linChuangBX = []
      }
    },

    // 获取治疗附加信息
    async getZhiLiaoFJXX(row, drugRow) {
      const res = await getZhiLiaoFJXX({
        bingLiID: this.bingLiID,
        yiZhuLB: this.tabActive,
        yiZhuMLID: drugRow.zhiLiaoData?.yiZhuMLID || drugRow.daiMa, // 医嘱目录ID
        yiZhuMLMC: drugRow.mingCheng, // 医嘱目录名称
        zhuYuanID: this.inpatientInit?.inPatientVo?.zhuYuanID
      })
      if (res.hasError === 0) {
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
        await drawerTableRef.handleUpdateRow(row, 'mingCheng', {
          ZLFJXX: res.data,
          ...drugRow,
          leiBie: drugRow.leiXing,
          guiGe: drugRow.zhiLiaoData?.zongJia || ''
        })
        return res.data
      }
    },
    // 检查治疗医嘱逻辑判断
    async checkZhiLiaoYiZhu(row, drugRow, ZLFJXX) {
      // 检查特定ID的治疗医嘱（转科医嘱）
      const yaoPinID = drugRow.daiMa
      if (yaoPinID && (yaoPinID.includes('6755') || yaoPinID.includes('25155'))) {
        // 检查出院录
        const res = await checkChuYuanLu({
          bingLiID: this.bingLiID
        })
        if (res.hasError === 0) {
          if (res.data === '1') {
            this.$message.error('开转科医嘱,请删除出院录后才能开医嘱')
            return
          } else {
            // 检查通过，继续处理社保限制支付范围
            await this.handleZhiLiaoXSP(row, ZLFJXX)
          }
        }
      } else {
        // 直接处理社保限制支付范围
        await this.handleZhiLiaoXSP(row, ZLFJXX)
      }
    },

    // 处理治疗医嘱明细及社保限制范围
    async handleZhiLiaoXSP(row, ZLFJXX) {
      if (!ZLFJXX || !ZLFJXX.yiZhuMXVos) {
        return
      }
      this.zhiLiaoYZMXWithSheBao = ZLFJXX.yiZhuMXVos.filter((item) => item.sheBaoSP)
      this.zhiLiaoYZMXWithoutSheBao = ZLFJXX.yiZhuMXVos.filter((item) => !item.sheBaoSP)

      if (this.zhiLiaoYZMXWithSheBao.length > 0) {
        // 保存当前医嘱数据，用于后续处理
        this.sheBaoYaoPinData = row
        this.zhiLiaoSheBaoDialog = true
      } else {
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
        await drawerTableRef.handleUpdateRow(row, 'zhiLiaoyzmxVos', ZLFJXX.yiZhuMXVos)
      }
    },

    // 治疗医嘱社保审批弹窗确定
    async handleZhiLiaoSheBaoConfirm(zhiLiaoYZMX) {
      const zhiLiaoyzmxVos = [...zhiLiaoYZMX, ...this.zhiLiaoYZMXWithoutSheBao]
      const drawerTableRef =
        this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
      await drawerTableRef.handleUpdateRow(this.sheBaoYaoPinData, 'zhiLiaoyzmxVos', zhiLiaoyzmxVos)
      this.zhiLiaoSheBaoDialog = false
    },

    // 治疗医嘱社保审批弹窗关闭
    handleZhiLiaoSheBaoClose() {
      if (this.sheBaoYaoPinData) {
        this.handleDeleteDrawerRow(this.sheBaoYaoPinData)
      }
      this.sheBaoYaoPinData = null
    },
    // 获取饮食附加信息
    async getYinShiFJXX(row, drugRow) {
      const res = await getYinShiFJXX({
        bingLiID: this.bingLiID,
        yinShiDM: drugRow.yinShiData?.daiMa || drugRow.daiMa, // 饮食代码
        zhuYuanID: this.inpatientInit?.inPatientVo?.zhuYuanID
      })
      if (res.hasError === 0) {
        const drawerTableRef =
          this.tabActive === 'cq' ? this.$refs.drawerTable : this.$refs.drawerTableTemp
        await drawerTableRef.handleUpdateRow(row, 'mingCheng', {
          YSFJXX: res.data,
          ...drugRow,
          leiBie: drugRow.leiXing,
          guiGe: drugRow.yinShiData.danJia
        })
        return res.data
      }
    },
    // 选中药品触发的删除
    handleDeleteDrawerRow(row) {
      const index = this.drawerYiZhuList.findIndex((item) => item === row)
      if (index !== -1) {
        this.drawerClickedRowIndex = index
        this.del()
      }
    },

    // 处理中选药品选择
    handleZhongXuanYaoPinSelect(row) {
      this.zhongXuanDialog = false
      // 向外发送事件
      this.$emit('zhongXuanDialogSelect', true)

      if (this.zhongXuanYaoPinData.row) {
        // 使用选中的新药品替换原来的药品
        this.yzLogic({
          row: this.zhongXuanYaoPinData.row,
          drugRow: row
        })
      }
    },
    // 处理中选药品弹窗关闭
    handleZhongXuanDialogClosed() {
      // 用户没有选择药品，只是关闭了弹窗
      this.$emit('zhongXuanDialogSelect', false)
      this.zhongXuanYaoPinData = {} // 清空存储的数据
    },

    // 处理中医辨证弹窗确认
    handleZhongYiBZDialogConfirm() {
      // 验证每个组是否同时选择了诊断和症状
      let errorMessage = ''

      for (const group of this.zhongYiBZTableData) {
        const hasSelectedZhenDuan = group.selectedZhenDuanIDs.length > 0
        const hasSelectedZhengZhuang = group.selectedZhengZhuangIDs.length > 0

        // 如果有诊断被选中，但没有症状被选中，或者有症状被选中，但没有诊断被选中，则为无效
        if (!hasSelectedZhenDuan || !hasSelectedZhengZhuang) {
          errorMessage = `中成药第${group.zuHao}组必须诊断与症状同时选中！`
          break
        }
      }

      if (errorMessage) {
        this.$alert(errorMessage, '提示信息')
        return
      }

      // 收集所有选中的诊断ID和症状ID
      const zhongChengYaoZD = []
      const zhongChengYaoZZ = []

      this.zhongYiBZTableData.forEach((group) => {
        zhongChengYaoZD.push(...group.selectedZhenDuanIDs)
        zhongChengYaoZZ.push(...group.selectedZhengZhuangIDs)
      })

      // 存储结果
      this.zhongYiBZResult = {
        zhongChengYaoZD,
        zhongChengYaoZZ
      }

      // 关闭弹窗并发送事件
      this.zhongYiBZDialog = false
      this.$emit('zhongYiBZDialogSelect', this.zhongYiBZResult)
    },
    // 显示中医辨证弹窗
    showZhongYiBZDialog(data) {
      this.zhongYiBZData = data
      this.zhongYiBZResult = {
        zhongChengYaoZD: [],
        zhongChengYaoZZ: []
      }
      this.processZhongYiBZData(data)
      this.zhongYiBZDialog = true
    },
    // 处理中医辩证弹窗取消
    async handleZhongYiBZDialogCancel() {
      await this.$alert(
        `中成药【${this.zhongYiBZData.yaoPinMC}】必须选择中医辩证信息！`,
        '提示信息'
      )
      this.$emit('zhongYiBZDialogSelect', false)
    },
    // 处理中医辨证数据，将其转换为表格数据
    processZhongYiBZData(data) {
      const { zhenDuanXX, zhengZhuangXX } = data
      const zuHaoMap = new Map()

      // 相同组号的诊断和症状放在一起
      if (zhenDuanXX && zhenDuanXX.length) {
        zhenDuanXX.forEach((item) => {
          const zuHao = item.zuHao
          if (!zuHaoMap.has(zuHao)) {
            zuHaoMap.set(zuHao, {
              zuHao,
              zhenDuanList: [],
              zhengZhuangList: [],
              selectedZhenDuanIDs: [],
              selectedZhengZhuangIDs: []
            })
          }
          zuHaoMap.get(zuHao).zhenDuanList.push(item)
        })
      }

      // 处理症状信息
      if (zhengZhuangXX && zhengZhuangXX.length) {
        zhengZhuangXX.forEach((item) => {
          const zuHao = item.zuHao
          if (!zuHaoMap.has(zuHao)) {
            zuHaoMap.set(zuHao, {
              zuHao,
              zhenDuanList: [],
              zhengZhuangList: [],
              selectedZhenDuanIDs: [],
              selectedZhengZhuangIDs: []
            })
          }
          zuHaoMap.get(zuHao).zhengZhuangList.push(item)
        })
      }

      // 转换为数组并排序
      this.zhongYiBZTableData = Array.from(zuHaoMap.values()).sort((a, b) => a.zuHao - b.zuHao)
    },

    dialogResult(resMsg, resultData) {
      //  resMsg返回值的内容  'resConditionsData':条件类返回，'blockade'：阻断，'noBlockade'：无阻断，'noConditionsData'：条件类中没有返回值，'noWarnData'：提醒类中没有返回值，'nonexecution':判断不需要走规则引擎，'error':接口报错或者因接口返回的数据结构改变而报错
      //  resultData为组件调用接口的接口返回值
      //  注意：resMsg = 'resConditionsData'（条件类返回）中，chaRuSX（插入顺序）的值对应传入参数的下标（例：传入['A药品', 'B药品']，chaRuSX为1时，对应'A药品'）
      //  根据返回回来的kiaajianz的含义7.阻断 ，2.记账无提醒，1.记账有提醒，10.自费(询问是否继续+特殊说明)，6.自费(询问是否继续)3.自费有提醒，4自费无提醒，然后做相应的业务操作。
      //  阻断:弹出询问内容的提示框，只有一个确定按钮，用户点击确定然后阻断返回，不让收费收下去。
      //  记账有提醒:暂时不用做任何操作。
      //  记账无提醒:暂时不用做任何操作。
      //  自费(询问是否继续):弹出提示内容，有确定和返回按钮。用户点击确定，根据项目ia将对应的材料设置成自费，然后收费下来。点击取消，即阻断返回。
      //  自费有提醒:弹出询问内容的提示框，只有一个确定按钮，用户点击确定 然后根据项目ia将对应的材料设置成自费，然后收费下来。
      //  自费无提醒:无提醒内容，本根据项目 id 将对应的材料设置成自费，然后收费下来。
      //  优先级：7.阻断 ，2.记账无提醒，1.记账有提醒，10.自费(询问是否继续+特殊说明)，6.自费(询问是否继续)，3.自费有提醒，4自费无提醒2：只有7.阻断、6.自费(询问是否继续)、3.自费有提醒有弹框
      this.PayingResultData = []
      switch (resMsg) {
        case 'resConditionsData':
          this.PayingResultData = resultData
          break
        case 'noConditionsData':
          this.$emit('PayingResult', 1)
          break
        case 'blockade':
          this.$emit('PayingResult', 3)
          break
        default:
          this.$emit('PayingResult', 1)
      }

      console.log(resMsg, resultData)
    },
    closeSelfPayingResult(resMsg, chaRuSX) {
      //  resMsg有7个值，分别对应 interdictConfirm：阻断的确定按钮，haveSpecialContinueExpenseConfirm：自费(询问是否继续+特殊说明)的确定按钮(有输入特殊说明)，specialContinueExpenseConfirm：自费(询问是否继续+特殊说明)的确定按钮(没有输入特殊说明)，specialContinueExpenseCancel：自费(询问是否继续+特殊说明)的取消按钮，continueExpenseConfirm：自费(询问是否继续)的确定按钮，continueExpenseCancel：自费(询问是否继续)的取消按钮，selfPayReminderConfirm：自费有提醒的确定按钮
      //  chaRuSX是当操作自费(询问是否继续+特殊说明)时，会返回的参数
      //  如果没有填特殊说明，点击确定，会设置成自费。若填了特殊说明，点击确定就不做业务操作，按自费(询问是否继续)流程走。
      let list = {}
      if (chaRuSX) {
        const fd = this.PayingResultData.filter((ev) => ev.chaRuSX === chaRuSX)
        if (fd && fd.length > 0) {
          list = fd[0]
        }
      }
      switch (resMsg) {
        case 'interdictConfirm':
          this.$emit('PayingResult', 3)
          break
        case 'haveSpecialContinueExpenseConfirm':
          this.$emit('PayingResult', 2, list)
          break
        case 'specialContinueExpenseConfirm':
          this.$emit('PayingResult', 2, list)
          break
        case 'specialContinueExpenseCancel':
          this.$emit('PayingResult', 3)
          break
        case 'continueExpenseConfirm':
          this.$emit('PayingResult', 2, list)
          break
        case 'continueExpenseCancel':
          this.$emit('PayingResult', 3)
          break
        case 'selfPayReminderConfirm':
          this.$emit('PayingResult', 2, list)
          break
      }
    },

    // 麻醉用途保存
    handleMzytSave() {
      if (this.mzytValue) {
        this.$emit('mzytSelect', this.mzytValue)
        this.mzytDialog = false
      } else {
        this.$alert('麻醉药品用途不能为空！', '信息窗口')
      }
    },
    // 麻醉用途关闭
    handleMzytClose() {
      this.$emit('mzytSelect', false)
      this.mzytDialog = false
    },

    // 社保审批弹窗确定
    handleSheBaoConfirm(value) {
      this.$emit('sheBaoSelect', value)
      this.sheBaoDialog = false
    },
    // 社保审批弹窗关闭
    handleSheBaoClose() {
      this.$emit('sheBaoSelect', false)
      this.sheBaoDialog = false
    },
    // 处方药诊断弹窗确认
    handleChuFangZDDialogConfirm() {
      if (this.chuFangZDList.length === 0) {
        this.$alert('请至少添加一个处方诊断', '信息窗口')
        return
      }

      // 验证处方诊断名称
      for (const item of this.chuFangZDList) {
        if (!item.mingChen || item.mingChen === '新增') {
          this.$alert('处方诊断名称不能为空', '信息窗口')
          return
        }
      }

      this.$emit('chuFangZDSelect', this.chuFangZDList)
      this.chuFangZDDialog = false
    },

    // 处方药诊断弹窗取消
    handleChuFangZDDialogCancel() {
      this.$emit('chuFangZDSelect', false)
      this.chuFangZDDialog = false
    },

    // 添加处方诊断
    handleAddChuFangZD() {
      // 处理空数组的情况，设置初始xuHao为1
      let xuHao = 1
      if (this.chuFangZDList.length > 0) {
        xuHao = this.chuFangZDList[this.chuFangZDList.length - 1].xuHao + 1
      }
      this.chuFangZDList.push({
        mingChen: '新增',
        icd: '',
        xuHao: xuHao
      })
    },

    // 删除处方诊断
    handleDeleteChuFangZD(index) {
      this.chuFangZDList.splice(index, 1)
    },

    // 上移处方诊断
    handleSortUp(index) {
      if (index > 0) {
        // 交换xuHao字段的值
        const currentXuHao = this.chuFangZDList[index].xuHao
        this.chuFangZDList[index].xuHao = this.chuFangZDList[index - 1].xuHao
        this.chuFangZDList[index - 1].xuHao = currentXuHao

        // 按xuHao排序
        this.chuFangZDList.sort((a, b) => a.xuHao - b.xuHao)
      }
    },

    // 下移处方诊断
    handleSortDown(index) {
      if (index < this.chuFangZDList.length - 1) {
        // 交换xuHao字段的值
        const currentXuHao = this.chuFangZDList[index].xuHao
        this.chuFangZDList[index].xuHao = this.chuFangZDList[index + 1].xuHao
        this.chuFangZDList[index + 1].xuHao = currentXuHao

        // 按xuHao排序
        this.chuFangZDList.sort((a, b) => a.xuHao - b.xuHao)
      }
    },

    // 打开处方诊断选择弹窗
    async handleSelectChuFangZD(index) {
      this.currentZhenDuanIndex = index
      this.icd = ''
      this.zhenDuanFilter = ''
      this.pageIndex = 1
      this.pageSize = 20
      this.zhenDuanListDialog = true
      this.isChuFangSelected = false
      // 加载诊断列表
      await this.loadZhenDuanList()
    },

    // 加载诊断列表
    async loadZhenDuanList() {
      try {
        const res = await getZhenDuanList({
          wenBen: this.zhenDuanFilter.toUpperCase(),
          pageIndex: this.pageIndex,
          pageSize: this.pageSize
        })

        if (res.hasError === 0) {
          this.zhenDuanListData = res.data || []
          if (res.extendData.total) {
            this.total = res.extendData.total
          }
        }
      } catch (error) {
        console.error('获取诊断列表失败', error)
        this.$message.error('获取诊断列表失败:' + error.errorMessage)
      }
    },

    // 搜索诊断
    async searchZhenDuan() {
      this.pageIndex = 1
      await this.loadZhenDuanList()
    },

    // 选择诊断
    handleSelectZhenDuan(row) {
      this.icd = row.icd
      this.zhenDuanFilter = row.zhenDuanMC
      this.isChuFangSelected = true
    },
    // 确认选择诊断
    handleSelectZhenDuanConfirm() {
      if (this.currentZhenDuanIndex >= 0) {
        // 保留原有的xuHao字段，只更新icd和mingChen
        const originalItem = this.chuFangZDList[this.currentZhenDuanIndex]
        this.$set(this.chuFangZDList, this.currentZhenDuanIndex, {
          ...originalItem,
          icd: this.icd,
          mingChen: this.zhenDuanFilter
        })
      }
      this.zhenDuanListDialog = false
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.loadZhenDuanList()
    },

    // 页码变化
    handleCurrentChange(val) {
      this.pageIndex = val
      this.loadZhenDuanList()
    },

    // 药品说明书
    openDrugInfo() {
      // 构建药品说明书URL，不传递医嘱ID
      const baseUrl = this.inpatientInit?.yaoPinSMS
      if (!baseUrl) {
        this.$message.warning('未配置药品说明书地址')
        return
      }

      // 移除@ypid@参数或设置为空
      const url = baseUrl.replace('@ypid@', '')
      const iWidth = 1200
      const iHeight = 800
      const iTop = (window.screen.availHeight - 30 - iHeight) / 2
      const iLeft = (window.screen.availWidth - 10 - iWidth) / 2

      window.open(
        url,
        '_blank',
        `height=${iHeight},width=${iWidth},top=${iTop},left=${iLeft},status=no,scroll=yes,resizable=no`
      )
    },

    // 右键菜单 - 药品说明书
    openDrugInfoWithOrderId(xiangMuID) {
      if (!xiangMuID) {
        this.$message.warning('未找到项目ID')
        return
      }

      const baseUrl = this.inpatientInit?.yaoPinSMS
      if (!baseUrl) {
        this.$message.warning('未配置药品说明书地址')
        return
      }

      const url = baseUrl.replace('@ypid@', xiangMuID)
      const iWidth = 1200
      const iHeight = 800
      const iTop = (window.screen.availHeight - 30 - iHeight) / 2
      const iLeft = (window.screen.availWidth - 10 - iWidth) / 2

      window.open(
        url,
        '_blank',
        `height=${iHeight},width=${iWidth},top=${iTop},left=${iLeft},status=no,scroll=yes,resizable=no`
      )
    },

    // 处理表格行右键菜单 - 直接打开药品说明书
    handleRowContextMenu(row, column, event) {
      event.preventDefault()

      // 只有当医嘱类别为'药品'时才执行药品说明书功能
      if (row.yiZhuLXMC !== '药品') {
        return // 对于非药品类别的医嘱，不执行任何操作
      }

      // 检查是否有项目ID
      if (!row.xiangMuID) {
        this.$message.warning('未找到项目ID')
        return
      }

      // 直接打开药品说明书
      this.openDrugInfoWithOrderId(row.xiangMuID)
    },

    // 患者医嘱360
    openPatientOrder360() {
      const url = this.inpatientInit?.huanZheYZ360
      if (!url) {
        this.$message.warning('未配置患者医嘱360地址')
        return
      }

      window.open(url, '_blank')
    },

    // 闭环展示
    openClosedLoopDisplay() {
      const baseUrl = this.inpatientInit?.biHuanZS
      const url = `${baseUrl}?as_tempid=${Math.random()}`

      window.open(url, '_blank')
    },

    // 不良事件上报
    openAdverseEventReport() {
      const url = this.inpatientInit?.buLiangSJSB
      if (!url) {
        this.$message.warning('未配置不良事件上报地址')
        return
      }

      const iWidth = 700
      const iHeight = 800
      const iTop = (window.screen.availHeight - 30 - iHeight) / 2
      const iLeft = (window.screen.availWidth - 10 - iWidth) / 2

      window.open(
        url,
        '_blank',
        `height=${iHeight},width=${iWidth},top=${iTop},left=${iLeft},status=no,scroll=yes,resizable=no`
      )
    },
    // 生成临时医嘱
    async copyToTempOrders() {
      const selectedRows = this.$refs.standingTable?.selection || []
      if (selectedRows.length === 0) {
        await this.$confirm('至少选中一条医嘱', '提示窗口', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return
      }
      try {
        // 分离有组号和无组号的医嘱
        const rowsWithZuHao = selectedRows.filter((row) => row.zuHao)
        const rowsWithoutZuHao = selectedRows.filter((row) => !row.zuHao)

        // 有组号时找出所有相同组号的医嘱
        const selectedZuHao = [...new Set(rowsWithZuHao.map((row) => row.zuHao))]
        const sameZuHaoRows = this.yiZhuList.filter((row) => selectedZuHao.includes(row.zuHao))

        // 合并所有选择医嘱
        const allStopRows = [...sameZuHaoRows, ...rowsWithoutZuHao]
        const yiZhuList = allStopRows.map((i) => i.val).toString()
        const res = await copyToLinShiYz({
          bingLiID: this.bingLiID,
          yiZhuList: yiZhuList
        })

        if (res.hasError === 0) {
          this.$message.success('生成临时医嘱成功')
          await this.getBingRenYZ()
        }
      } catch (error) {
        console.error('生成临时医嘱失败', error)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.inpatient-view {
  background: #eff3fb;
  padding: 8px 8px 0;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  ::v-deep .el-dialog__footer {
    border-top: 1px solid #dadee5;
  }

  ::v-deep .el-tabs--border-card {
    background: #eff3fb;
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs__header .el-tabs__nav {
      .el-tabs__item {
        height: 32px;
        line-height: 32px;
        background: #ffffff;
        border: 1px solid #dcdfe6;
        &.is-active {
          background: #eff3fb;
          border-bottom-color: transparent;
        }
      }
    }
    .el-tabs__content {
      padding: 10px;
      flex: 1;
      min-height: 0;
      .el-tab-pane {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .filter-box {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #eaf0f9;
    display: flex;
    align-items: center;
    padding: 4px 10px;
    .el-dropdown,
    .el-checkbox {
      margin-left: var(--common-margin);
      margin-right: 0;
    }
  }
  .purple-button {
    background: var(--color-purple);
    border: 1px solid var(--color-purple);
    &:hover,
    &:focus {
      background: #ce8be0;
      border-color: #ce8be0;
    }
  }
  .inpatient-content {
    padding-top: 4px;
    position: relative;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    .title {
      font-weight: 600;
      border-left: 4px solid #356ac5;
      padding-left: 8px;
      margin-bottom: 8px;
    }
    .table-container {
      flex: 1;
      min-height: 0;
      position: relative;
      ::v-deep .el-table--mini .el-table__cell {
        padding: 0 4px;
        .cell {
          padding: 0;
        }
      }

      ::v-deep .el-table__column-filter-trigger {
        .el-icon-arrow-down {
          color: #333;
          font-size: 14px;
          transform: scale(1);
        }
      }
      ::v-deep .el-table__fixed {
        height: calc(100% - 7px) !important;
      }

      ::v-deep .el-drawer__wrapper {
        position: absolute;
        pointer-events: none;
        .el-drawer__header {
          padding: 4px 8px;
          height: 30px;
          .drawer-header {
            display: flex;
            align-items: center;
            position: relative;
            .title {
              margin-bottom: 0;
              margin-right: 8px;
            }
            .full-button {
              position: absolute;
              top: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 68px;
              height: 16px;
              text-align: center;
              line-height: 16px;
              font-size: 16px;
              background: #ecf1f9;
              border-radius: 0 0 6px 6px;
              cursor: pointer;
            }
          }
        }
        .el-drawer__container {
          pointer-events: none;
        }

        .el-drawer {
          pointer-events: auto;
        }
      }
    }
    .footer-action {
      margin-top: 8px;
      height: 40px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #eaf0f9;
      display: flex;
      align-items: center;
      padding-left: 10px;
      .el-checkbox {
        margin-left: var(--common-margin);
        margin-right: 0;
      }
    }
    ::v-deep .el-table {
      .el-checkbox__input.is-disabled {
        display: none;
      }
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
        &:after {
          height: 8px;
          left: 5px;
        }
      }
      .selected-row td.el-table__cell {
        background-color: rgba(53, 106, 197, 0.5);
        color: #fff;
        border-color: transparent;
      }
      .blue-row td.el-table__cell {
        color: #356ac5;
      }
      .red-row td.el-table__cell {
        color: #f35656;
      }
      .hover-row:not(.selected-row) td.el-table__cell {
        background: #eff3fb;
      }
    }
  }
}
.execute-dialog-content {
  .label {
    display: inline-block;
    width: 80px;
    text-align: right;
    margin-right: 10px;
  }
}
.history-drug-dialog {
  ::v-deep .el-dialog__body {
    .drug-filter {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .el-radio-group {
        margin-right: 10px;
      }
      .el-date-picker {
        width: 260px;
      }
    }
  }
}
.virtual-drug-dialog {
  ::v-deep .el-dialog__body {
    .drug-filter {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .el-input {
        width: 500px;
      }
    }
  }
}
.personal-muban {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .el-input {
    width: 200px;
  }
}
.save-mb-header {
  border: 1px solid #dcdfe6;
  margin-bottom: 10px;
  .save-title {
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    padding: 0 12px;
    background: #eaf0f9;
  }
  .el-radio-group {
    padding: 10px 12px;
  }
  .el-input {
    width: 200px;
  }
}

.zhong-yi-dialog {
  max-height: 600px;
  border: 1px solid #dcdfe6;
  &-header {
    height: 40px;
    border-bottom: 1px solid #dcdfe6;
    display: flex;

    .header-left {
      width: 200px;
      height: 40px;
      line-height: 40px;
      border-right: 1px solid #dcdfe6;
      background: #e9eff7;
      font-weight: bold;
      text-align: right;
    }

    .header-right {
      flex: 1;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
    }
  }

  &-content {
    padding: 10px;
  }
}
.mzyt-dialog {
  ::v-deep .el-radio-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0 10px;

    .el-radio {
      margin: 0;
    }
  }
}

.tbmj-descriptions {
  ::v-deep .el-descriptions-item__label {
    width: 150px;
    text-align: right;
    padding-right: 0;
  }
}

.zykd-descriptions {
  ::v-deep .el-descriptions-item__label {
    width: 100px;
    text-align: right;
    padding-right: 0;
    background: #eaf0f9;
  }

  ::v-deep .el-descriptions-item__content {
    padding: 4px;
  }
  ::v-deep .is-bordered .el-descriptions-item__cell {
    border: 1px solid #dcdfe6;
  }

  .el-input,
  .el-textarea,
  .el-select {
    width: 100%;
  }

  .clear-count {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;

    .clear {
      color: #409eff;
      cursor: pointer;
    }

    .count {
      color: #909399;
    }
  }
}

.zykd-form {
  padding: 10px 10px 0;
  border: 1px solid #dcdfe6;

  .el-form-item {
    margin-bottom: 18px;
  }

  .clear-count {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;

    .clear {
      color: #409eff;
      cursor: pointer;
    }

    .count {
      color: #909399;
    }
  }
}

.chu-fang-zd-dialog {
  border: 1px solid #dcdfe6;
  .chu-fang-zd-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 10px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;

    .header-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }

  .chu-fang-zd-content {
    padding: 10px;

    .zd-name-link {
      color: #409eff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .sort-buttons {
      display: flex;
      justify-content: center;
    }
  }
}

.zhen-duan-list-dialog {
  .zd-name-link {
    color: #409eff;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
  .search-bar {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .zhen-duan-table {
    margin-bottom: 15px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
  }
}

.kang-jun-yp-dialog {
  .usage-method-section {
    .usage-method-options {
      display: flex;
      flex-direction: column;
      gap: 0;
      border: 1px solid #dcdfe6;
      max-height: 700px;
      overflow-y: auto;

      .usage-method-item {
        border-bottom: 1px solid #dcdfe6;

        &:last-child {
          border-bottom: none;
        }

        .usage-method-radio {
          margin-left: 12px;
          display: block;
          height: 40px;
          line-height: 40px;

          ::v-deep .el-radio__label {
            padding-left: 8px;
            font-weight: 500;
          }
        }

        .prevention-sub-options {
          background-color: #eff3fb;
          border-top: 1px solid #dcdfe6;

          &.disabled {
            background-color: #f5f5f5;
            color: #c0c4cc;
          }

          // 确保三列等高显示
          ::v-deep .el-row {
            display: flex;
            align-items: stretch;
          }

          ::v-deep .el-col {
            display: flex;
            flex-direction: column;
          }
        }
      }
    }
  }

  .option-column {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    ::v-deep .el-radio-group,
    ::v-deep .el-checkbox-group {
      padding: 12px;
    }

    .radio-item,
    .checkbox-item {
      margin-bottom: 6px;
      height: 20px;

      ::v-deep .el-radio,
      ::v-deep .el-checkbox {
        margin-right: 0;
        width: 100%;

        .el-radio__label,
        .el-checkbox__label {
          white-space: normal;
          word-break: break-all;
          font-size: 14px;
        }
      }
    }

    &.option-column-first,
    &.option-column-middle {
      border-right: 1px solid #dcdfe6;
    }

    &.option-column-last {
      border-right: none;
    }

    ::v-deep .el-radio-group,
    ::v-deep .el-checkbox-group {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

/* 高危因素弹窗样式 */

.gao-wei-ys-container {
  .checkbox-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-top: 6px;
    .title {
      height: 40px;
      padding-left: 16px;
      margin: 0;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .table-color-dark {
    background: #eaf0f9;
  }
  .table-color-light {
    background: #f6f6f6;
  }
  .checkbox-container {
    .checkbox-setting {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .el-checkbox {
      height: 40px;
      padding-left: 16px;
      margin: 0;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #dcdfe6;
      &:last-child {
        border-bottom: none;
      }
    }
  }
}

.approval-section {
  padding-top: 20px;
  margin-bottom: 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  span:nth-child(2) {
    font-weight: 600;
  }
}

/* 新生儿抗生素预防使用问卷弹窗样式 */
.newborn-questionnaire-container {
  .question-section {
    background-color: #f8f9fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .question-title {
      background-color: #eaf0f9;
      height: 40px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      font-weight: 500;
      border-bottom: 1px solid #dcdfe6;
    }

    .question-content {
      .el-checkbox-group {
        width: 100%;

        .content-item {
          padding: 12px;
          color: #333;

          .el-checkbox {
            width: 100%;
            display: flex;
            align-items: center;
            ::v-deep .el-checkbox__input {
              line-height: 1.2;
            }

            ::v-deep .el-checkbox__label {
              line-height: 1.2;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
      }

      .content-item {
        padding: 12px;
        color: #333;
        border-bottom: 1px solid #dcdfe6;

        // 奇数行
        &:nth-child(2n + 1) {
          background-color: #f6f6f6;
        }

        &:nth-child(2n) {
          background-color: #eff3fb;
        }

        &:last-child {
          border-bottom: none;
        }
      }
      .other {
        display: flex;
        align-items: center;
        .el-textarea {
          flex: 1;
        }
      }
    }
  }
}

/* 微生物送检情况弹窗样式 */
.wei-sheng-wu-container {
  .question-section {
    background-color: #f8f9fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .question-title {
      background-color: #eaf0f9;
      height: 40px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      font-weight: 500;
      border-bottom: 1px solid #dcdfe6;

      span {
        font-size: 14px;
        color: #303133;
      }
    }

    .question-content {
      .content-item {
        padding: 12px;
        color: #333;
        border-bottom: 1px solid #dcdfe6;
        transition: background-color 0.2s ease;

        // 奇数行
        &:nth-child(2n + 1) {
          background-color: #f6f6f6;
        }

        &:nth-child(2n) {
          background-color: #eff3fb;
        }

        &:last-child {
          border-bottom: none;
        }

        // 选中状态
        &.selected {
          background-color: #e6f7ff !important;
          border-left: 3px solid #1890ff;
        }

        .option-radio {
          width: 100%;
          display: flex;
          align-items: flex-start;
          margin: 0;

          ::v-deep .el-radio__input {
            line-height: 1.2;
            margin-top: 2px;
          }

          ::v-deep .el-radio__label {
            line-height: 1.5;
            white-space: normal;
            word-break: break-all;
            padding-left: 8px;
            font-size: 14px;
          }
        }

        // 文本输入框样式
        .el-textarea {
          width: 100%;

          ::v-deep .el-textarea__inner {
            resize: none;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }
}
</style>
