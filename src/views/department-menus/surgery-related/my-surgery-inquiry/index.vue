<template>
  <!-- 我的相关手术查询 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-col">
        <!-- 日期范围选择 -->
        <div class="filter-item">
          <span class="filter-label">手术日期：</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            size="mini"
            style="width: 240px"
          ></el-date-picker>
        </div>

        <!-- 病人姓名 -->
        <div class="filter-item">
          <span class="filter-label">病人姓名：</span>
          <el-input
            v-model="patientName"
            placeholder="请输入病人姓名"
            size="mini"
            style="width: 150px"
            clearable
          ></el-input>
        </div>
      </div>

      <div class="filter-col">
        <!-- 病案号 -->
        <div class="filter-item">
          <span class="filter-label">病案号：</span>
          <el-input
            v-model="admissionNumber"
            placeholder="请输入病案号"
            size="mini"
            style="width: 150px"
            clearable
          ></el-input>
        </div>

        <!-- 病人专科 -->
        <div class="filter-item">
          <span class="filter-label">病人专科：</span>
          <el-select
            v-model="selectedDepartment"
            placeholder="请选择专科"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in [{ buMenID: '0', buMenMC: '所有专科' }, ...zhuanKeList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
      </div>

      <div class="filter-col">
        <div class="filter-item">
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>我的相关手术查询</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
      >
        <el-table-column prop="shouShuJian" label="手术间" min-width="80"></el-table-column>
        <el-table-column prop="bingAnHao" label="病案号" min-width="100"></el-table-column>
        <el-table-column prop="bingRenXM" label="姓名" min-width="80"></el-table-column>
        <el-table-column label="年龄" min-width="60">
          <template #default="{ row }">
            <span>{{ calculateAge(row.chuShengRQ) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="bingRenXBMC" label="性别" min-width="60"></el-table-column>
        <el-table-column prop="zhuanKeMC" label="专科" min-width="80"></el-table-column>
        <el-table-column prop="bingQuMC" label="病区" min-width="80"></el-table-column>
        <el-table-column prop="chuangWeiHao" label="床位" min-width="60"></el-table-column>
        <el-table-column label="拟施手术" min-width="150">
          <template #default="{ row }">
            <span>{{ row.niShiSS && row.niShiSS[0] ? row.niShiSS[0].shouShuMC : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="zhuDaoYSXM" label="主刀" min-width="80"></el-table-column>
        <el-table-column prop="taiShangZDXM" label="台上指导" min-width="80"></el-table-column>
        <el-table-column prop="diYiZSXM" label="一助" min-width="80"></el-table-column>
        <el-table-column prop="diErZSXM" label="二助" min-width="80"></el-table-column>
        <el-table-column prop="diSanZSXM" label="三助" min-width="80"></el-table-column>
        <el-table-column prop="diSiZSXM" label="四助" min-width="80"></el-table-column>
        <el-table-column prop="shuZhongHZ" label="术中会诊" min-width="80"></el-table-column>
        <el-table-column prop="maZuiYS1XM" label="主麻" min-width="80"></el-table-column>
        <el-table-column prop="maZuiYS2XM" label="二麻" min-width="80"></el-table-column>
        <el-table-column prop="maZuiYS3XM" label="三麻" min-width="80"></el-table-column>
        <el-table-column prop="xunHuiHS1XM" label="巡回" min-width="80"></el-table-column>
        <el-table-column prop="shuQianDDSC" label="术前等待时长" min-width="80"></el-table-column>
        <el-table-column prop="yuJiSSSC" label="预计时长" min-width="80"></el-table-column>
        <el-table-column prop="zhuangTaiBZ" label="状态" min-width="80"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getBenRenXGSS } from '@/api/surgery-related'
import { mapState } from 'vuex'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'

export default {
  name: 'MySurgeryInquiry',
  data() {
    return {
      // 查询条件
      dateRange: [],
      patientName: '',
      admissionNumber: '',
      selectedDepartment: '0',
      // 表格数据
      tableData: []
    }
  },
  computed: {
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo,
      zhuanKeList: ({ patient }) => patient.zhuanKeList,
      doctorInfo: ({ patient }) => patient.doctorInfo
    })
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      // 设置默认日期为当天
      const today = format(new Date(), 'yyyy-MM-dd')
      this.dateRange = [today, today]

      // 初始查询
      await this.handleQuery()
    },

    // 查询数据
    async handleQuery() {
      try {
        const params = {
          bingRenXM: this.patientName.trim(),
          bingAnHao: this.admissionNumber.trim(),
          zhuanKeID: this.selectedDepartment,
          yiShengRykID: this.doctorInfo?.renYuanKuID
        }

        // 日期范围
        if (this.dateRange && this.dateRange.length === 2) {
          params.kaiShiSJ = this.dateRange[0]
          params.jieShuSJ = this.dateRange[1]
        }

        const res = await getBenRenXGSS(params)

        if (res && res.hasError === 0 && res.data) {
          this.tableData = res.data || []
        }
      } catch (error) {
        this.$message.error(error.errorMessage || '查询数据失败')
        this.tableData = []
      }
    },

    // 计算年龄
    calculateAge(birthDate) {
      if (!birthDate) return '-'
      const birth = new Date(birthDate)
      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age >= 0 ? age : '-'
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            手术间: item.shouShuJian || '',
            病案号: item.bingAnHao || '',
            姓名: item.bingRenXM || '',
            年龄: this.calculateAge(item.chuShengRQ),
            性别: item.bingRenXBMC || '',
            专科: item.zhuanKeMC || '',
            病区: item.bingQuMC || '',
            床位: item.chuangWeiHao || '',
            拟施手术: item.niShiSS && item.niShiSS[0] ? item.niShiSS[0].shouShuMC : '',
            主刀: item.zhuDaoYSXM || '',
            台上指导: item.taiShangZDXM || '',
            一助: item.diYiZSXM || '',
            二助: item.diErZSXM || '',
            三助: item.diSanZSXM || '',
            四助: item.diSiZSXM || '',
            术中会诊: item.shuZhongHZ || '',
            主麻: item.maZuiYS1XM || '',
            二麻: item.maZuiYS2XM || '',
            三麻: item.maZuiYS3XM || '',
            巡回: item.xunHuiHS1XM || '',
            术前等待时长: item.shuQianDDSC || '',
            预计时长: item.yuJiSSSC ? `${item.yuJiSSSC}分钟` : '',
            状态: item.zhuangTaiBZ || ''
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `我的相关手术查询_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.filter-col {
  display: flex;
  padding: 0 8px;

  .filter-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
  }
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
  white-space: nowrap;
  text-align: right;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
