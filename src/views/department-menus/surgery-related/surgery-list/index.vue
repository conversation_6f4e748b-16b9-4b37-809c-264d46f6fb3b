<template>
  <!-- 手术一览表 -->
  <div class="container">
    <!-- 查询条件区域 -->
    <div class="filter-container">
      <div class="filter-col">
        <!-- 手术日期 -->
        <div class="filter-item">
          <span class="filter-label">手术日期：</span>
          <el-date-picker
            v-model="searchForm.shouShuRQ"
            type="date"
            placeholder="选择手术日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            size="mini"
            style="width: 150px"
          />
        </div>

        <!-- 住院号 -->
        <div class="filter-item">
          <span class="filter-label">住院号：</span>
          <el-input
            v-model="searchForm.zhuYuanHao"
            placeholder="请输入住院号"
            size="mini"
            style="width: 150px"
            clearable
          />
        </div>
      </div>

      <div class="filter-col">
        <!-- 病案号 -->
        <div class="filter-item">
          <span class="filter-label">病案号：</span>
          <el-input
            v-model="searchForm.bingAnHao"
            placeholder="请输入病案号"
            size="mini"
            style="width: 150px"
            clearable
          />
        </div>

        <!-- 手术专科 -->
        <div class="filter-item">
          <span class="filter-label">手术专科：</span>
          <el-select
            v-model="searchForm.zhuanKeID"
            placeholder="请选择专科"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in zhuanKeList"
              :key="item.buMenID || item.daiMa || item.zhuanKeID"
              :label="item.buMenMC || item.mingCheng || item.zhuanKeMC"
              :value="item.buMenID || item.daiMa || item.zhuanKeID"
            />
          </el-select>
        </div>
      </div>

      <div class="filter-col">
        <div class="filter-item">
          <el-button
            type="primary"
            size="mini"
            class="purple-button"
            :loading="loading"
            @click="handleSearch"
          >
            查询
          </el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-container">
      <div class="title">
        <span>手术一览表</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
      >
        <el-table-column prop="shouShuJian" label="手术间" min-width="80" />
        <el-table-column prop="xingMing" label="姓名" min-width="100" />
        <el-table-column label="年龄" min-width="60">
          <template #default="{ row }">
            <span>{{ calculateAge(row.chuShengRQ) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="xingBie" label="性别" min-width="60" />
        <el-table-column prop="bingQu" label="病区" min-width="100" />
        <el-table-column prop="chuangWei" label="床位" min-width="60" />
        <el-table-column prop="niShiShouShu" label="拟施手术" min-width="150" />
        <el-table-column prop="zhuDao" label="主刀" min-width="80" />
        <el-table-column prop="yiZhu" label="一助" min-width="80" />
        <el-table-column prop="erZhu" label="二助" min-width="80" />
        <el-table-column prop="zhuMa" label="主麻" min-width="80" />
        <el-table-column prop="erMa" label="二麻" min-width="80" />
        <el-table-column prop="sanMa" label="三麻" min-width="80" />
        <el-table-column prop="xunHui" label="巡回" min-width="80" />
        <el-table-column prop="qiXie" label="器械" min-width="80" />
        <el-table-column prop="yuJiDengDaiShiJian" label="预计手术等待时间" min-width="120" />
        <el-table-column prop="yuJiShiChang" label="预计时长" min-width="80" />
        <el-table-column prop="zhuangTai" label="状态" min-width="80" />
      </el-table>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { format } from 'date-fns'

export default {
  name: 'SurgeryList',
  data() {
    return {
      loading: false,
      // 查询条件
      searchForm: {
        shouShuRQ: format(new Date(), 'yyyy-MM-dd'), // 默认当天
        zhuYuanHao: '',
        bingAnHao: '',
        zhuanKeID: ''
      },
      // 表格数据 - 临时模拟数据
      tableData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeList: ({ patient }) => patient.zhuanKeList
    })
  },
  async mounted() {
    await this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        await this.getZhuanKeList()
        // 初始化完成后，加载一次数据
        await this.getSurgeryList()
      } catch (error) {
        this.$message.error('初始化数据失败')
      }
    },
    // 获取专科列表
    async getZhuanKeList() {
      try {
        await this.$store.dispatch('patient/getZhuanKeList')
        console.log('专科列表数据:', this.zhuanKeList)
      } catch (error) {
        console.error('获取专科列表失败:', error)
      }
    },
    // 获取手术一览表数据
    async getSurgeryList() {
      this.loading = true
      try {
        // TODO: 待接口提供后，替换为真实接口调用
        // const params = {
        //   shouShuRQ: this.searchForm.shouShuRQ,
        //   zhuYuanHao: this.searchForm.zhuYuanHao,
        //   bingAnHao: this.searchForm.bingAnHao,
        //   zhuanKeID: this.searchForm.zhuanKeID
        // }
        // const res = await getSurgeryList(params)
        // if (res.hasError === 0) {
        //   this.tableData = res.data || []
        // } else {
        //   this.$message.error(res.errorMessage || '获取数据失败')
        //   this.tableData = []
        // }

        // 临时模拟数据
        await this.loadMockData()
      } catch (error) {
        this.$message.error('获取数据失败')
        this.tableData = []
        console.error('获取手术一览表失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 加载模拟数据
    async loadMockData() {
      // 模拟网络延迟
      await new Promise((resolve) => setTimeout(resolve, 500))

      // 临时模拟数据 - 待真实接口对接后删除
      this.tableData = [
        {
          shouShuJian: '手术间1',
          xingMing: '张三',
          chuShengRQ: '1980-05-15',
          xingBie: '男',
          bingQu: '外科一病区',
          chuangWei: '101',
          niShiShouShu: '阑尾切除术',
          zhuDao: '李医生',
          yiZhu: '王医生',
          erZhu: '赵医生',
          zhuMa: '陈医生',
          erMa: '刘医生',
          sanMa: '',
          xunHui: '护士A',
          qiXie: '护士B',
          yuJiDengDaiShiJian: '30分钟',
          yuJiShiChang: '120分钟',
          zhuangTai: '准备中'
        },
        {
          shouShuJian: '手术间2',
          xingMing: '李四',
          chuShengRQ: '1975-08-20',
          xingBie: '女',
          bingQu: '外科二病区',
          chuangWei: '205',
          niShiShouShu: '胆囊切除术',
          zhuDao: '张医生',
          yiZhu: '孙医生',
          erZhu: '',
          zhuMa: '周医生',
          erMa: '吴医生',
          sanMa: '郑医生',
          xunHui: '护士C',
          qiXie: '护士D',
          yuJiDengDaiShiJian: '15分钟',
          yuJiShiChang: '90分钟',
          zhuangTai: '进行中'
        },
        {
          shouShuJian: '手术间3',
          xingMing: '王五',
          chuShengRQ: '1990-12-10',
          xingBie: '男',
          bingQu: '骨科病区',
          chuangWei: '308',
          niShiShouShu: '骨折内固定术',
          zhuDao: '马医生',
          yiZhu: '冯医生',
          erZhu: '朱医生',
          zhuMa: '许医生',
          erMa: '',
          sanMa: '',
          xunHui: '护士E',
          qiXie: '护士F',
          yuJiDengDaiShiJian: '45分钟',
          yuJiShiChang: '180分钟',
          zhuangTai: '已完成'
        }
      ]
    },
    // 计算年龄
    calculateAge(birthDate) {
      if (!birthDate) return '-'
      const birth = new Date(birthDate)
      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age >= 0 ? age : '-'
    },
    // 查询按钮点击事件
    handleSearch() {
      this.getSurgeryList()
    },
    // 重置按钮点击事件
    handleReset() {
      this.searchForm = {
        shouShuRQ: format(new Date(), 'yyyy-MM-dd'),
        zhuYuanHao: '',
        bingAnHao: '',
        zhuanKeID: ''
      }
      this.getSurgeryList()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.filter-col {
  display: flex;

  .filter-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
}

.filter-label {
  font-weight: bold;
  white-space: nowrap;
  text-align: right;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
