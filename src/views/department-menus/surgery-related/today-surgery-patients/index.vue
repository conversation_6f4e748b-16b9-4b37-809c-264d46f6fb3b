<template>
  <!-- 今日手术病人一览表 -->
  <div class="container"></div>
</template>

<script>
import { getTodaySurgeryPatientList } from '@/api/surgery-related'

export default {
  name: 'TodaySurgeryPatients',
  data() {
    return {}
  },
  mounted() {
    this.getTodaySurgeryPatientList()
  },
  methods: {
    async getTodaySurgeryPatientList() {
      try {
        const res = await getTodaySurgeryPatientList({
          page: 1,
          rows: 10,
          shouShuRQ: '2012-08-08'
        })
        if (res.hasError === 0) {
          console.log(res.data)
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  /* 组件样式 */
}
</style>
