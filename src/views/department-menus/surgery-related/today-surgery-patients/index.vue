<template>
  <!-- 今日手术病人一览表 -->
  <div class="container">
    <!-- 查询条件区域 -->
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="手术日期：">
          <el-date-picker
            v-model="searchForm.shouShuRQ"
            type="date"
            placeholder="选择手术日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item label="病案号：">
          <el-input
            v-model="searchForm.bingAnHao"
            placeholder="请输入病案号"
            style="width: 160px"
            clearable
          />
        </el-form-item>
        <el-form-item label="手术间：">
          <el-select
            v-model="searchForm.shouShuJian"
            placeholder="请选择手术间"
            style="width: 160px"
            clearable
          >
            <el-option
              v-for="item in shouShuJianList"
              :key="item.shouShuJianDM || item.daiMa"
              :label="item.shouShuJianMC || item.mingCheng"
              :value="item.shouShuJianDM || item.daiMa"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开单病区：">
          <el-select
            v-model="searchForm.kaiDanBQID"
            placeholder="请选择开单病区"
            style="width: 160px"
            clearable
          >
            <el-option
              v-for="item in bingQuList"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="当前病区：">
          <el-select
            v-model="searchForm.bingQuID"
            placeholder="请选择当前病区"
            style="width: 160px"
            clearable
          >
            <el-option
              v-for="item in bingQuList"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="专科：">
          <el-select
            v-model="searchForm.zhuanKeID"
            placeholder="请选择专科"
            style="width: 160px"
            clearable
          >
            <el-option
              v-for="item in zhuanKeList"
              :key="item.buMenID || item.daiMa || item.zhuanKeID"
              :label="item.buMenMC || item.mingCheng || item.zhuanKeMC"
              :value="item.buMenID || item.daiMa || item.zhuanKeID"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100vh - 280px)"
      >
        <el-table-column prop="BQMC" label="开单病区" width="120" />
        <el-table-column prop="XBQMC" label="当前病区" width="120" />
        <el-table-column prop="ZKMC" label="专科" width="120" />
        <el-table-column prop="EMPI" label="病案号" width="120" />
        <el-table-column prop="CWH" label="床位号" width="80" />
        <el-table-column prop="BRXM" label="姓名" width="100" />
        <el-table-column prop="BRXB" label="性别" width="60" />
        <el-table-column prop="CSRQ" label="出生日期" width="120" />
        <el-table-column prop="SSJ" label="手术间" width="100" />
        <el-table-column prop="NSSSJ" label="拟施手术时间" width="150" />
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.rows"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { getTodaySurgeryPatientList } from '@/api/surgery-related'
import { getShouShuJianList } from '@/api/surgical-notice'
import { getBingQuList } from '@/api/medical-quality'
import { mapState } from 'vuex'
import { format } from 'date-fns'

export default {
  name: 'TodaySurgeryPatients',
  computed: {
    ...mapState({
      zhuanKeList: ({ patient }) => patient.zhuanKeList
    })
  },
  data() {
    return {
      loading: false,
      // 查询条件
      searchForm: {
        shouShuRQ: format(new Date(), 'yyyy-MM-dd'), // 默认当天
        bingAnHao: '',
        shouShuJian: '',
        kaiDanBQID: '',
        bingQuID: '',
        zhuanKeID: ''
      },
      // 下拉选项数据
      shouShuJianList: [],
      bingQuList: [],
      // 表格数据
      tableData: [],
      // 分页信息
      pagination: {
        page: 1,
        rows: 10,
        total: 0
      }
    }
  },
  async mounted() {
    await this.initData()
    // 初始化完成后，可以加载一次数据
    this.getTodaySurgeryPatientList()
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        await Promise.all([this.getShouShuJianList(), this.getBingQuList(), this.getZhuanKeList()])
      } catch (error) {
        this.$message.error('初始化数据失败')
      }
    },
    // 获取手术间列表
    async getShouShuJianList() {
      try {
        const res = await getShouShuJianList({})
        if (res.hasError === 0) {
          this.shouShuJianList = res.data || []
          console.log('手术间列表数据:', this.shouShuJianList)
        }
      } catch (error) {
        console.error('获取手术间列表失败:', error)
      }
    },
    // 获取病区列表
    async getBingQuList() {
      try {
        const res = await getBingQuList()
        if (res.hasError === 0) {
          this.bingQuList = res.data || []
          console.log('病区列表数据:', this.bingQuList)
        }
      } catch (error) {
        console.error('获取病区列表失败:', error)
      }
    },
    // 获取专科列表
    async getZhuanKeList() {
      try {
        await this.$store.dispatch('patient/getZhuanKeList')
        console.log('专科列表数据:', this.zhuanKeList)
      } catch (error) {
        console.error('获取专科列表失败:', error)
      }
    },
    // 获取今日手术病人列表
    async getTodaySurgeryPatientList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          rows: this.pagination.rows,
          ...this.searchForm
        }

        const res = await getTodaySurgeryPatientList(params)
        if (res.hasError === 0) {
          this.tableData = res.data?.data || []
          this.pagination.total = res.data?.total || 0
        } else {
          this.$message.error(res.errorMessage || '获取数据失败')
          this.tableData = []
          this.pagination.total = 0
        }
      } catch (error) {
        this.$message.error('获取数据失败')
        this.tableData = []
        this.pagination.total = 0
        console.error('获取今日手术病人列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 查询按钮点击事件
    handleSearch() {
      this.pagination.page = 1 // 重置到第一页
      this.getTodaySurgeryPatientList()
    },
    // 重置按钮点击事件
    handleReset() {
      this.searchForm = {
        shouShuRQ: format(new Date(), 'yyyy-MM-dd'),
        bingAnHao: '',
        shouShuJian: '',
        kaiDanBQID: '',
        bingQuID: '',
        zhuanKeID: ''
      }
      this.pagination.page = 1
      this.getTodaySurgeryPatientList()
    },
    // 每页显示条数改变
    handleSizeChange(val) {
      this.pagination.rows = val
      this.pagination.page = 1 // 重置到第一页
      this.getTodaySurgeryPatientList()
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTodaySurgeryPatientList()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  background-color: #fff;
  height: 100vh;
  overflow: hidden;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
}
</style>
